// SpaceInvadersGameLogic.tsx
// This file contains the main game update logic extracted from SpaceInvaders.tsx

import { useCallback } from "react";
import {
  checkPlayerLevelUp,
  getPlayerHealthBoostOnLevelUp,
  INACTIVITY_CONSTANTS,
  getInactivityState,
} from "./gameLogic/difficultyManager";
import {
  Asteroid,
  manageAsteroidSpawning,
  updateAsteroids,
  handleAsteroidCollisions,
} from "./gameLogic/asteroidManager";
import { UpgradeManager } from "./gameLogic/upgrades/upgradeManager";
import { Upgrade as UpgradeDefinition } from "./gameLogic/upgrades/definitions";
import {
  useEnemySystem,
  Enemy,
  SpecialShip,
  Crate,
  EnemySystemDependencies
} from "./enemySystem";

// Re-export interfaces that are used by the game logic
export interface GameObject {
  x: number;
  y: number;
  width: number;
  height: number;
  vx?: number;
  vy?: number;
}

export interface Player extends GameObject {
  health: number;
  maxHealth: number;
  fireRate: number;
  lastShot: number;
  powerLevel: number;
  activeMultiShotLevel: number;
  multiShotLevelExpireTime: number;
  autoHealCharges: number;
  lastSignificantMoveX: number;
  timeStationaryMs: number;
  isBlinkingRed?: boolean;
  lastPenaltyAppliedAtSecondTier: number;
  invincibilityEndTime: number;
  killStreak: number;
}

// Enemy interface now imported from enemySystem.tsx

export interface Bullet extends GameObject {
  damage: number;
  isPlayerBullet: boolean;
}

interface PowerUp extends GameObject {
  type: "health" | "fireRate" | "multiShot" | "shield";
  duration?: number;
}

export interface Particle extends GameObject {
  life: number;
  maxLife: number;
  color: string;
}

// SpecialShip and Crate interfaces now imported from enemySystem.tsx

interface FloatingText {
  text: string;
  x: number;
  y: number;
  life: number;
  maxLife: number;
  color: string;
  isStreakBonus?: boolean;
}

interface Explosion {
  x: number;
  y: number;
  radius: number;
  maxRadius: number;
  life: number;
  maxLife: number;
  color: string;
}

interface GameInternalState {
  player: Player;
  enemies: Enemy[];
  bullets: Bullet[];
  powerUps: PowerUp[];
  particles: Particle[];
  floatingTexts: FloatingText[];
  explosions: Explosion[];
  asteroids: Asteroid[];
  keys: Record<string, boolean>;
  lastEnemySpawn: number;
  lastAsteroidSpawn: number;
  lastSoloAsteroidSpawn: number;
  enemySpawnRate: number;
  lastTime: number;
  shakeIntensity: number;
  shakeDecay: number;
  isBossActiveRef: boolean;
  lastBossSpawnBlockRef: number;
  globalBossPowerLevelRef: number;
  barrierLine: number;
  specialShip: SpecialShip | null;
  crate: Crate | null;
  lastBarrierBoostSpawnLevelBlock: number;
  lastAutoHealSpawnLevelBlock: number;
  totalRunTimeMs: number;
  gameSpeed: number;
  timeDilationEndTime: number;
  isMaxStreakActive: boolean;
}

export interface GameSettings {
  enemySpawnRate: number;
  playerFireRate: number;
  enemyBulletSpeed: number;
  playerBulletSpeed: number;
  musicVolume: number;
  sfxVolume: number;
  musicEnabled: boolean;
  sfxEnabled: boolean;
  controlScheme: "arrows" | "wasd";
}

export type GameState =
  | "menu"
  | "playing"
  | "gameOver"
  | "options"
  | "submitScore"
  | "leaderboard"
  | "upgrading";

// Constants
const BASE_CANVAS_WIDTH = 507;
const BASE_CANVAS_HEIGHT = 900;
const PLAYER_SPEED = 8;

// Game logic dependencies interface
export interface GameLogicDependencies {
  gameStateRef: React.MutableRefObject<GameInternalState>;
  audioRefs: React.MutableRefObject<{
    playerShoot: HTMLAudioElement | null;
    playerHit: HTMLAudioElement | null;
    enemyHit: HTMLAudioElement | null;
    enemyKill: HTMLAudioElement | null;
    powerupPickup: HTMLAudioElement | null;
    backgroundMusic: HTMLAudioElement | null;
  }>;
  upgradeManagerRef: React.MutableRefObject<UpgradeManager>;
  backgroundAnimationRef: React.MutableRefObject<number>;
  crtAnimationRef: React.MutableRefObject<number>;
  settings: GameSettings;
  level: number;
  enemiesKilled: number;
  isMobile: boolean;
  setScore: React.Dispatch<React.SetStateAction<number>>;
  setLevel: React.Dispatch<React.SetStateAction<number>>;
  setEnemiesKilled: React.Dispatch<React.SetStateAction<number>>;
  setGameState: (newState: GameState | ((prevState: GameState) => GameState)) => void;
  setUpgradeOptions: React.Dispatch<React.SetStateAction<UpgradeDefinition[]>>;
  setIsMaxStreakActive: React.Dispatch<React.SetStateAction<boolean>>;
  getMovementKeys: () => { left: string; right: string; shoot: string };
}

// This will be populated with the extracted functions
export const useGameLogicHooks = (dependencies: GameLogicDependencies) => {
  const {
    gameStateRef,
    audioRefs,
    upgradeManagerRef,
    backgroundAnimationRef,
    crtAnimationRef,
    settings,
    level,
    enemiesKilled,
    isMobile,
    setScore,
    setLevel,
    setEnemiesKilled,
    setGameState,
    setUpgradeOptions,
    setIsMaxStreakActive,
    getMovementKeys,
  } = dependencies;

  const playSound = useCallback(
    (soundType: keyof typeof audioRefs.current) => {
      if (!settings.sfxEnabled && soundType !== "backgroundMusic") return;
      if (!settings.musicEnabled && soundType === "backgroundMusic") return;

      const audio = audioRefs.current[soundType];
      if (audio) {
        audio.volume =
          soundType === "backgroundMusic"
            ? settings.musicVolume
            : settings.sfxVolume;
        audio.playbackRate = gameStateRef.current.gameSpeed;
        if (soundType !== "backgroundMusic") audio.currentTime = 0;
        audio.play().catch(() => {});
      }
    },
    [settings, audioRefs, gameStateRef]
  );

  const createParticles = useCallback(
    (x: number, y: number, count: number, color: string) => {
      for (let i = 0; i < count; i++) {
        gameStateRef.current.particles.push({
          x: x + Math.random() * 20 - 10,
          y: y + Math.random() * 20 - 10,
          width: 2,
          height: 2,
          vx: (Math.random() - 0.5) * 8,
          vy: (Math.random() - 0.5) * 8,
          life: 60,
          maxLife: 60,
          color,
        });
      }
    },
    [gameStateRef]
  );

  const checkCollision = useCallback(
    (a: GameObject, b: GameObject) =>
      a.x < b.x + b.width &&
      a.x + a.width > b.x &&
      a.y < b.y + b.height &&
      a.y + a.height > b.y,
    []
  );

  // Enemy system initialization
  const enemySystemDependencies: EnemySystemDependencies = {
    gameStateRef,
    settings,
    level,
  };
  const enemySystem = useEnemySystem(enemySystemDependencies);
  const { createEnemy, spawnEnemies, spawnSpecialBonuses } = enemySystem;

  // spawnEnemies function now provided by enemy system

  // spawnSpecialBonuses function now provided by enemy system

  const updateGame = useCallback(
    (currentTime: number) => {
      const state = gameStateRef.current;
      const dt = currentTime - state.lastTime;
      if (dt <= 0) {
        return;
      }
      state.lastTime = currentTime;

      // Time Dilation Logic
      const DILATION_DURATION = 5000; // 5 seconds
      if (currentTime < state.timeDilationEndTime) {
        const timePassed =
          DILATION_DURATION - (state.timeDilationEndTime - currentTime);
        const progress = timePassed / DILATION_DURATION;
        state.gameSpeed = 0.1 + 0.9 * progress; // Ramp from 0.1 to 1.0
      } else {
        state.gameSpeed = 1.0;
      }

      // Adjust audio playback rate based on game speed
      Object.values(audioRefs.current).forEach((audio) => {
        if (audio) {
          audio.playbackRate = state.gameSpeed;
        }
      });

      const scaledDt = dt * state.gameSpeed;

      state.totalRunTimeMs += scaledDt;
      backgroundAnimationRef.current += scaledDt * 0.001;
      crtAnimationRef.current += scaledDt * 0.01;

      const player = state.player;
      const playerXBeforeMove = player.x;
      const moveKeys = getMovementKeys();

      if (
        player.activeMultiShotLevel > 0 &&
        currentTime > player.multiShotLevelExpireTime
      ) {
        player.activeMultiShotLevel--;
        if (player.activeMultiShotLevel > 0) {
          player.multiShotLevelExpireTime = currentTime + 10000;
        } else {
          player.multiShotLevelExpireTime = 0;
        }
      }
      player.powerLevel = 1 + player.activeMultiShotLevel;

      const effectivePlayerSpeed = PLAYER_SPEED * state.gameSpeed;

      const fireBullet = () => {
        const bulletSpreadLevel =
          upgradeManagerRef.current.getUpgradeLevel("bulletSpread");
        const shouldSpread = Math.random() < bulletSpreadLevel / 100;

        if (shouldSpread) {
          for (let i = 0; i < player.powerLevel; i++) {
            const offX =
              player.powerLevel > 1
                ? (i - (player.powerLevel - 1) / 2) * 35
                : 0;
            const spawnX = player.x + player.width / 2 - 2 + offX;
            state.bullets.push({
              x: spawnX,
              y: player.y,
              width: 4,
              height: 10,
              vy: -12 * settings.playerBulletSpeed,
              damage: 1,
              isPlayerBullet: true,
            });
            state.bullets.push({
              x: spawnX,
              y: player.y,
              width: 4,
              height: 10,
              vx: -4,
              vy: -11 * settings.playerBulletSpeed,
              damage: 1,
              isPlayerBullet: true,
            });
            state.bullets.push({
              x: spawnX,
              y: player.y,
              width: 4,
              height: 10,
              vx: 4,
              vy: -11 * settings.playerBulletSpeed,
              damage: 1,
              isPlayerBullet: true,
            });
          }
        } else {
          for (let i = 0; i < player.powerLevel; i++) {
            const offX =
              player.powerLevel > 1
                ? (i - (player.powerLevel - 1) / 2) * 35
                : 0;
            state.bullets.push({
              x: player.x + player.width / 2 - 2 + offX,
              y: player.y,
              width: 4,
              height: 10,
              vy: -12 * settings.playerBulletSpeed,
              damage: 1,
              isPlayerBullet: true,
            });
          }
        }
        player.lastShot = currentTime;
        playSound("playerShoot");
      };

      // Player movement and shooting logic
      if (isMobile) {
        if (state.keys["touchLeft"] && player.x > 0)
          player.x -= effectivePlayerSpeed;
        if (
          state.keys["touchRight"] &&
          player.x < BASE_CANVAS_WIDTH - player.width
        )
          player.x += effectivePlayerSpeed;
        if (
          currentTime - player.lastShot >
          player.fireRate / settings.playerFireRate / state.gameSpeed
        ) {
          fireBullet();
        }
      } else {
        if (state.keys[moveKeys.left] && player.x > 0)
          player.x -= effectivePlayerSpeed;
        if (
          state.keys[moveKeys.right] &&
          player.x < BASE_CANVAS_WIDTH - player.width
        )
          player.x += effectivePlayerSpeed;
        if (
          state.keys[moveKeys.shoot] &&
          currentTime - player.lastShot >
            player.fireRate / settings.playerFireRate / state.gameSpeed
        ) {
          fireBullet();
        }
      }

      // Anti-camping logic
      const playerMovedHorizontallyThisFrame = player.x !== playerXBeforeMove;
      if (
        playerMovedHorizontallyThisFrame &&
        Math.abs(player.x - player.lastSignificantMoveX) >=
          INACTIVITY_CONSTANTS.SIGNIFICANT_MOVE_PX
      ) {
        player.timeStationaryMs = 0;
        player.isBlinkingRed = false;
        player.lastSignificantMoveX = player.x;
        player.lastPenaltyAppliedAtSecondTier =
          INACTIVITY_CONSTANTS.PENALTY_START_THRESHOLD_S - 1;
      } else if (!playerMovedHorizontallyThisFrame) {
        player.timeStationaryMs += scaledDt;
      }

      const inactivityResult = getInactivityState(
        player.timeStationaryMs,
        player.lastPenaltyAppliedAtSecondTier
      );
      player.isBlinkingRed = inactivityResult.shouldBlink;
      if (inactivityResult.healthPenalty > 0) {
        player.health -= inactivityResult.healthPenalty;
        player.lastPenaltyAppliedAtSecondTier =
          inactivityResult.newLastAppliedPenaltyTier;
        createParticles(
          player.x + player.width / 2,
          player.y + player.height / 2,
          5,
          "#cc0000"
        );
        playSound("playerHit");
        state.shakeIntensity = 3;
      }

      // Auto-heal logic
      if (!isMobile && state.keys["h"]) {
        if (player.autoHealCharges > 0 && player.health < player.maxHealth) {
          player.health = player.maxHealth;
          player.autoHealCharges--;
          playSound("powerupPickup");
          createParticles(
            player.x + player.width / 2,
            player.y + player.height / 2,
            15,
            "#88ff88"
          );
          state.keys["h"] = false;
        } else {
          state.keys["h"] = false;
        }
      }

      // Spawn enemies and bonuses
      spawnEnemies(currentTime);
      spawnSpecialBonuses(currentTime);

      // Asteroid management
      const spawnResult = manageAsteroidSpawning(
        currentTime,
        state.lastAsteroidSpawn,
        state.lastSoloAsteroidSpawn,
        BASE_CANVAS_WIDTH
      );
      if (spawnResult.newAsteroids.length > 0) {
        state.asteroids.push(...spawnResult.newAsteroids);
      }
      state.lastAsteroidSpawn = spawnResult.newLastWaveSpawnTime;
      state.lastSoloAsteroidSpawn = spawnResult.newLastSoloSpawnTime;

      updateAsteroids(state.asteroids, state.gameSpeed, currentTime);

      // Special ship logic
      if (state.specialShip) {
        state.specialShip.x += state.specialShip.vx * state.gameSpeed;
        if (
          state.specialShip.vx > 0 &&
          state.specialShip.x > BASE_CANVAS_WIDTH
        ) {
          state.specialShip = null;
        } else if (
          state.specialShip.vx < 0 &&
          state.specialShip.x + state.specialShip.width < 0
        ) {
          state.specialShip = null;
        } else if (
          state.specialShip &&
          state.specialShip.dropDelayTimer > 0 &&
          currentTime >= state.specialShip.dropDelayTimer &&
          !state.specialShip.hasDroppedCrate
        ) {
          if (!state.crate) {
            const CRATE_WIDTH = 20;
            const CRATE_HEIGHT = 20;
            const CRATE_FALL_SPEED = 2;
            const newCrate: Crate = {
              x:
                state.specialShip.x +
                state.specialShip.width / 2 -
                CRATE_WIDTH / 2,
              y:
                state.specialShip.y +
                state.specialShip.height / 2 -
                CRATE_HEIGHT / 2,
              width: CRATE_WIDTH,
              height: CRATE_HEIGHT,
              vy: CRATE_FALL_SPEED,
              type: state.specialShip.type,
              spawnTime: currentTime,
            };
            state.crate = newCrate;
            playSound("powerupPickup");
          }
          state.specialShip.hasDroppedCrate = true;
          state.specialShip = null;
        }
      }

      // Crate logic
      if (state.crate) {
        state.crate.y += state.crate.vy * state.gameSpeed;
        if (checkCollision(state.player, state.crate)) {
          if (state.crate.type === "barrierBoost") {
            state.barrierLine = Math.max(0, state.barrierLine - 5);
          } else if (state.crate.type === "autoHeal") {
            state.player.autoHealCharges = Math.min(
              1,
              (state.player.autoHealCharges || 0) + 1
            );
          }
          createParticles(
            state.crate.x + state.crate.width / 2,
            state.crate.y + state.crate.height / 2,
            15,
            "#ffff88"
          );
          playSound("powerupPickup");
          state.crate = null;
        } else if (state.crate.y > BASE_CANVAS_HEIGHT) {
          state.crate = null;
        }
      }

      // Barrier line logic handled by enemy system

      // Enemy movement handled by enemy system
      enemySystem.updateEnemyMovement(currentTime);

      // Enemy AI and shooting logic
      state.enemies.forEach((e: Enemy) => {

        // Boss AI logic
        if (e.type === "boss") {
          const TELEGRAPH_DURATION = 1000;
          const PATTERN_SWITCH_INTERVAL = 8000;
          if (
            e.attackPattern === undefined ||
            e.nextPatternSwitchTime === undefined ||
            e.isTelegraphing === undefined ||
            e.telegraphCompleteTime === undefined
          ) {
            e.attackPattern = "multiSpread";
            e.nextPatternSwitchTime = currentTime + PATTERN_SWITCH_INTERVAL;
            e.isTelegraphing = false;
            e.telegraphCompleteTime = 0;
            e.telegraphColor = undefined;
          }
          if (currentTime > e.nextPatternSwitchTime && !e.isTelegraphing) {
            e.attackPattern =
              e.attackPattern === "multiSpread"
                ? "focusedBarrage"
                : "multiSpread";
            e.nextPatternSwitchTime = currentTime + PATTERN_SWITCH_INTERVAL;
            e.isTelegraphing = true;
            e.telegraphCompleteTime = currentTime + TELEGRAPH_DURATION;
            e.telegraphColor =
              e.attackPattern === "multiSpread"
                ? "rgba(255, 100, 100, 0.7)"
                : "rgba(100, 100, 255, 0.7)";
          }
          if (currentTime - e.lastShot > e.fireRate / state.gameSpeed) {
            let canFireNow = false;
            if (e.isTelegraphing) {
              if (currentTime > e.telegraphCompleteTime) {
                e.isTelegraphing = false;
                e.telegraphColor = undefined;
                canFireNow = true;
              }
            } else {
              canFireNow = true;
            }
            if (canFireNow && e.powerLevel && e.powerLevel > 0) {
              if (e.attackPattern === "multiSpread") {
                for (let i = 0; i < e.powerLevel; i++) {
                  const offX =
                    e.powerLevel > 1 ? (i - (e.powerLevel - 1) / 2) * 20 : 0;
                  state.bullets.push({
                    x: e.x + e.width / 2 - 2 + offX,
                    y: e.y + e.height,
                    width: 4,
                    height: 10,
                    vy: 5 * settings.enemyBulletSpeed,
                    damage: 15,
                    isPlayerBullet: false,
                  });
                }
              } else if (e.attackPattern === "focusedBarrage") {
                for (let i = 0; i < e.powerLevel; i++) {
                  const offX = (i - (e.powerLevel - 1) / 2) * 5;
                  state.bullets.push({
                    x: e.x + e.width / 2 - 2 + offX,
                    y: e.y + e.height,
                    width: 3,
                    height: 12,
                    vy: 7 * settings.enemyBulletSpeed,
                    damage: 12,
                    isPlayerBullet: false,
                  });
                }
              }
              e.lastShot = currentTime;
            }
          }
        } else {
          // Regular enemy shooting logic
          if (currentTime - e.lastShot > e.fireRate / state.gameSpeed) {
            let attemptShot = false;
            let fireDiagonally = false;
            const baseBulletSpeed = 6 * settings.enemyBulletSpeed;

            const enemyIdForLog = `${e.type}_${e.x.toFixed(0)}_${e.y.toFixed(0)}`;

            if (level >= 21 && (e.type === "fast" || e.type === "heavy")) {
              const SPECIAL_ENEMY_SHOOT_PROBABILITY = 0.5;
              if (Math.random() < SPECIAL_ENEMY_SHOOT_PROBABILITY) {
                attemptShot = true;
                const DIAGONAL_CHANCE = 0.15;
                if (Math.random() < DIAGONAL_CHANCE) {
                  console.log(
                    `[DIAG_DEBUG L${level}] Eligible enemy ${enemyIdForLog} PASSED 15% DIAGONAL CHANCE. FIRING DIAGONAL!`
                  );
                  fireDiagonally = true;
                }
              }
            } else {
              const STANDARD_ENEMY_SHOOT_PROBABILITY = 0.005;
              if (Math.random() < STANDARD_ENEMY_SHOOT_PROBABILITY) {
                attemptShot = true;
              }
            }

            if (attemptShot) {
              let bulletVx = 0;
              let bulletVy = baseBulletSpeed;

              if (fireDiagonally) {
                const angleRad = 30 * (Math.PI / 180);
                bulletVy = baseBulletSpeed * Math.cos(angleRad);
                const horizontalSpeedComponent =
                  baseBulletSpeed * Math.sin(angleRad);
                bulletVx =
                  Math.random() < 0.5
                    ? -horizontalSpeedComponent
                    : horizontalSpeedComponent;
                console.log(
                  `[DIAG_DEBUG L${level}] Pushing DIAGONAL bullet for ${enemyIdForLog}: vx=${bulletVx.toFixed(2)}, vy=${bulletVy.toFixed(2)}`
                );
              }

              state.bullets.push({
                x: e.x + e.width / 2 - 2,
                y: e.y + e.height,
                width: 4,
                height: 8,
                vx: bulletVx,
                vy: bulletVy,
                damage: 10,
                isPlayerBullet: false,
              });
              e.lastShot = currentTime;
            }
          }
        }
      });

      // Update bullets and particles
      state.bullets.forEach((b: Bullet) => {
        b.x += (b.vx || 0) * state.gameSpeed;
        b.y += (b.vy || 0) * state.gameSpeed;
      });
      state.particles.forEach((p: Particle) => {
        p.x += (p.vx || 0) * state.gameSpeed;
        p.y += (p.vy || 0) * state.gameSpeed;
        p.life -= state.gameSpeed;
      });
      state.floatingTexts.forEach((ft) => {
        ft.y -= 0.5 * state.gameSpeed;
        ft.life -= state.gameSpeed;
      });
      state.explosions.forEach((exp) => {
        exp.life -= scaledDt;
        exp.radius = exp.maxRadius * (1 - exp.life / exp.maxLife);
      });

      // Bullet collision detection
      state.bullets.forEach((bullet: Bullet, bIdx: number) => {
        if (bullet.isPlayerBullet) {
          let bulletRemoved = false;
          state.enemies.forEach((enemy: Enemy, eIdx: number) => {
            if (bulletRemoved) return;

            if (checkCollision(bullet, enemy)) {
              bulletRemoved = true;
              state.bullets.splice(bIdx, 1);

              // Explosive Johnny Logic
              const explosiveJohnnyLevel =
                upgradeManagerRef.current.getUpgradeLevel("explosiveJohnny");
              if (
                explosiveJohnnyLevel > 0 &&
                Math.random() < explosiveJohnnyLevel * 0.005
              ) {
                const explosionRadius = 50;
                const explosionDamage = bullet.damage * 0.25;

                state.explosions.push({
                  x: bullet.x,
                  y: bullet.y,
                  radius: 0,
                  maxRadius: explosionRadius,
                  life: 300,
                  maxLife: 300,
                  color: "rgba(255, 140, 0, 0.8)",
                });

                playSound("enemyKill");

                state.enemies.forEach((otherEnemy) => {
                  if (otherEnemy !== enemy) {
                    const dx =
                      otherEnemy.x +
                      otherEnemy.width / 2 -
                      (enemy.x + enemy.width / 2);
                    const dy =
                      otherEnemy.y +
                      otherEnemy.height / 2 -
                      (enemy.y + enemy.height / 2);
                    if (Math.sqrt(dx * dx + dy * dy) < explosionRadius) {
                      otherEnemy.health -= explosionDamage;
                      createParticles(
                        otherEnemy.x + otherEnemy.width / 2,
                        otherEnemy.y + otherEnemy.height / 2,
                        5,
                        "#ffcc00"
                      );
                    }
                  }
                });
              }

              enemy.health -= bullet.damage;
              createParticles(
                enemy.x + enemy.width / 2,
                enemy.y + enemy.height / 2,
                8,
                "var(--red-secondary-ff4444)"
              );
              playSound("enemyHit");

              const vampireGiftLevel =
                upgradeManagerRef.current.getUpgradeLevel("vampireGift");
              if (
                vampireGiftLevel > 0 &&
                Math.random() < vampireGiftLevel * 0.001
              ) {
                player.health = Math.min(player.maxHealth, player.health + 10);
                state.floatingTexts.push({
                  text: "+10",
                  x: player.x + player.width / 2,
                  y: player.y,
                  life: 60,
                  maxLife: 60,
                  color: "#00ff00",
                });
              }

              if (enemy.health <= 0) {
                const greedIsGoodLevel =
                  upgradeManagerRef.current.getUpgradeLevel("greedIsGood");
                const scoreMultiplier = 1 + greedIsGoodLevel * 0.02;

                // Streak Logic
                player.killStreak++;
                const streakMilestones = [10, 25, 50];
                let streakBonus = 0;
                let milestoneReached = -1;

                for (let i = streakMilestones.length - 1; i >= 0; i--) {
                  if (player.killStreak >= streakMilestones[i]) {
                    streakBonus = streakMilestones[i];
                    if (player.killStreak === streakMilestones[i]) {
                      milestoneReached = streakMilestones[i];
                    }
                    break;
                  }
                }

                if (player.killStreak >= 50) {
                  state.isMaxStreakActive = true;
                }

                if (milestoneReached !== -1) {
                  state.floatingTexts.push({
                    text: `+${milestoneReached}`,
                    x: BASE_CANVAS_WIDTH / 2,
                    y: BASE_CANVAS_HEIGHT / 2,
                    life: 60,
                    maxLife: 60,
                    color: "#ffff007d",
                    isStreakBonus: true,
                  });
                }

                setScore(
                  (s) =>
                    s + Math.floor(enemy.points * scoreMultiplier) + streakBonus
                );

                setEnemiesKilled((ek) => ek + 1);
                if (enemy.type === "boss") {
                  state.isBossActiveRef = false;
                  if (state.barrierLine < 11) state.barrierLine++;
                }
                state.enemies.splice(eIdx, 1);
                createParticles(
                  enemy.x + enemy.width / 2,
                  enemy.y + enemy.height / 2,
                  15,
                  "var(--red-main-ff0000)"
                );
                state.shakeIntensity = 5;
                playSound("enemyKill");
                if (Math.random() < 0.15) {
                  const types: PowerUp["type"][] = [
                    "health",
                    "fireRate",
                    "multiShot",
                  ];
                  state.powerUps.push({
                    x: enemy.x,
                    y: enemy.y,
                    width: 20,
                    height: 20,
                    vy: 2,
                    type: types[Math.floor(Math.random() * types.length)],
                  });
                }
              }
            }
          });

          // Special ship collision
          if (
            !bulletRemoved &&
            state.specialShip &&
            !state.specialShip.hasDroppedCrate &&
            state.specialShip.dropDelayTimer === 0 &&
            checkCollision(bullet, state.specialShip)
          ) {
            state.specialShip.health -= bullet.damage;
            state.bullets.splice(bIdx, 1);
            createParticles(
              state.specialShip.x + state.specialShip.width / 2,
              state.specialShip.y + state.specialShip.height / 2,
              8,
              "#88ddff"
            );
            playSound("enemyHit");
            if (state.specialShip.health <= 0) {
              state.specialShip.dropDelayTimer = currentTime + 500;
            }
          }
        } else if (checkCollision(bullet, player)) {
          // Player collision with enemy bullets
          if (currentTime > player.invincibilityEndTime) {
            player.health -= bullet.damage;
            player.killStreak = 0; // Reset streak
            state.isMaxStreakActive = false; // Reset max streak flag
            playSound("playerHit");
            state.shakeIntensity = 8;
            createParticles(
              player.x + player.width / 2,
              player.y + player.height / 2,
              5,
              "#ff6666"
            );

            const solInvictusLevel =
              upgradeManagerRef.current.getUpgradeLevel("solInvictus");
            if (solInvictusLevel > 0) {
              player.invincibilityEndTime =
                currentTime + solInvictusLevel * 250;
            }
          } else {
            createParticles(
              player.x + player.width / 2,
              player.y + player.height / 2,
              3,
              "#ffff00"
            );
          }
          state.bullets.splice(bIdx, 1);
        }
      });

      // Power-up collision detection
      state.powerUps.forEach((pUp: PowerUp, idx: number) => {
        pUp.y += (pUp.vy || 0) * state.gameSpeed;
        if (checkCollision(pUp, player)) {
          if (pUp.type === "health")
            player.health = Math.min(player.maxHealth, player.health + 30);
          else if (pUp.type === "fireRate")
            player.fireRate = Math.max(50, player.fireRate - 30);
          else if (pUp.type === "multiShot") {
            player.activeMultiShotLevel = Math.min(
              5,
              player.activeMultiShotLevel + 1
            );
            player.multiShotLevelExpireTime = currentTime + 10000;
          }
          setScore((s) => s + 5);
          state.powerUps.splice(idx, 1);
          createParticles(pUp.x + 10, pUp.y + 10, 10, "#ff8888");
          playSound("powerupPickup");
        }
      });

      // Asteroid collision handling
      const collisionResults = handleAsteroidCollisions(
        state.asteroids,
        player,
        state.bullets,
        createParticles,
        playSound as (sound: string) => void,
        currentTime
      );
      if (collisionResults.playerHealthLost > 0) {
        if (currentTime > player.invincibilityEndTime) {
          player.health -= collisionResults.playerHealthLost;
          player.killStreak = 0; // Reset streak
          state.isMaxStreakActive = false; // Reset max streak flag
          playSound("playerHit");
          state.shakeIntensity = 10;
          createParticles(
            player.x + player.width / 2,
            player.y + player.height / 2,
            20,
            "#ff4444"
          );

          const solInvictusLevel =
            upgradeManagerRef.current.getUpgradeLevel("solInvictus");
          if (solInvictusLevel > 0) {
            player.invincibilityEndTime = currentTime + solInvictusLevel * 250;
          }
        }
      }
      if (collisionResults.bulletsToRemove.size > 0) {
        state.bullets = state.bullets.filter(
          (b) => !collisionResults.bulletsToRemove.has(b)
        );
      }

      // Clean up off-screen objects
      state.bullets = state.bullets.filter(
        (b: Bullet) => b.y > -20 && b.y < BASE_CANVAS_HEIGHT + 20
      );
      state.enemies = state.enemies.filter(
        (e: Enemy) => e.y < BASE_CANVAS_HEIGHT + 50
      );
      state.powerUps = state.powerUps.filter(
        (p: PowerUp) => p.y < BASE_CANVAS_HEIGHT
      );
      state.asteroids = state.asteroids.filter(
        (a: Asteroid) => a.y < BASE_CANVAS_HEIGHT + a.height
      );
      state.particles = state.particles.filter((p: Particle) => p.life > 0);
      state.floatingTexts = state.floatingTexts.filter((ft) => ft.life > 0);
      state.explosions = state.explosions.filter((exp) => exp.life > 0);
      state.shakeIntensity *= state.shakeDecay;

      // Level up check
      if (checkPlayerLevelUp(enemiesKilled, level)) {
        const newLevel = level + 1;
        setLevel(newLevel);
        player.health = Math.min(
          player.maxHealth,
          player.health + getPlayerHealthBoostOnLevelUp()
        );

        if (newLevel > 1 && newLevel % 5 === 0) {
          setUpgradeOptions(upgradeManagerRef.current.getUpgradeOptions());
          setGameState("upgrading");
        }
      }

      // Game over check
      if (player.health <= 0) {
        const isGodMode =
          import.meta.env.DEV && localStorage.getItem("devGodMode") === "true";
        if (isGodMode) {
          player.health = player.maxHealth;
          console.log("[DevUtils] God Mode: Player health restored!");
          createParticles(
            player.x + player.width / 2,
            player.y + player.height / 2,
            20,
            "#00ff00"
          );
        } else {
          setGameState("submitScore");
          if (audioRefs.current.backgroundMusic)
            audioRefs.current.backgroundMusic.pause();
        }
      }

      // Update max streak state
      setIsMaxStreakActive(state.isMaxStreakActive);
    },
    [
      gameStateRef,
      audioRefs,
      backgroundAnimationRef,
      crtAnimationRef,
      getMovementKeys,
      upgradeManagerRef,
      settings,
      level,
      enemiesKilled,
      isMobile,
      playSound,
      createParticles,
      checkCollision,
      spawnEnemies,
      spawnSpecialBonuses,
      enemySystem,
      setScore,
      setLevel,
      setEnemiesKilled,
      setGameState,
      setUpgradeOptions,
      setIsMaxStreakActive,
    ]
  );

  return {
    playSound,
    createParticles,
    checkCollision,
    createEnemy,
    spawnEnemies,
    spawnSpecialBonuses,
    updateGame,
  };
};
