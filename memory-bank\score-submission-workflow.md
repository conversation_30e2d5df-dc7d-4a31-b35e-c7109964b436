# Score Submission Workflow Analysis

The score submission process is a key feature that integrates gameplay with the project's social dimension via Bluesky. The flow is designed to be robust, handling both verified and anonymous submissions.

The process unfolds as follows:

1.  **Initiation (Game Over)**: The flow begins on the `GameOverScreen`. If a score has not yet been saved for the session, a "SUBMIT SCORE" button is available. Clicking this button changes the `gameState` to `submitScore`, which triggers the rendering of the submission UI.

2.  **UI Presentation (`ScoreSubmissionScreen`)**: The `ScoreSubmissionScreen` component is displayed. This component is responsible for collecting the user's Bluesky handle or handling the choice to submit anonymously.

3.  **User Interaction & Data Flow**:
    *   **Bluesky Submission**: The user enters their handle into an input field. The `handleSubmitScore` function (passed down from a parent component, likely `src/SpaceInvaders.tsx`) is invoked on button click. This function orchestrates the backend call for verification and saving.
    *   **Anonymous Submission**: The user can opt to submit anonymously, which triggers a confirmation step. If confirmed, the `handleAnonymousSubmit` function is called to save the score without a handle.

4.  **Backend Logic (Convex & Bluesky API)**:
    *   **Handle Verification**: The `handleSubmitScore` function calls a Convex action, likely located in `convex/social.ts`, which communicates with the external Bluesky API to verify the provided handle.
    *   **Score Persistence**: Upon successful verification (or if submitted anonymously), the `saveScore` mutation in `convex/index.ts` is called. This mutation writes the final score, level, kills, game duration, and player identity (handle or anonymous) to the `scores` table in the database, as defined in `convex/schema.ts`.

## Visualizing the Flow

```mermaid
sequenceDiagram
    participant User
    participant GameOverScreen as GameOverScreen.tsx
    participant SpaceInvaders as SpaceInvaders.tsx (State Manager)
    participant ScoreSubmissionScreen as gameUi.tsx
    participant ConvexBackend as Convex (index.ts, social.ts)
    participant BlueskyAPI as Bluesky API

    User->>GameOverScreen: Clicks "SUBMIT SCORE"
    GameOverScreen->>SpaceInvaders: setGameState('submitScore')
    SpaceInvaders-->>ScoreSubmissionScreen: Renders ScoreSubmissionScreen

    alt Submit with Bluesky
        User->>ScoreSubmissionScreen: Enters Bluesky Handle
        User->>ScoreSubmissionScreen: Clicks "Submit with Bluesky"
        ScoreSubmissionScreen->>SpaceInvaders: Calls handleSubmitScore()
        SpaceInvaders->>ConvexBackend: Calls verifyAndSaveScore(handle, score, ...)
        ConvexBackend->>BlueskyAPI: resolveHandle(handle)
        BlueskyAPI-->>ConvexBackend: Returns verification status
        ConvexBackend->>ConvexBackend: If verified, saveScore() to DB
        ConvexBackend-->>SpaceInvaders: Returns success/error
        SpaceInvaders-->>ScoreSubmissionScreen: Updates UI (e.g., verification error)
    else Submit Anonymously
        User->>ScoreSubmissionScreen: Clicks "Submit Anonymously"
        ScoreSubmissionScreen->>ScoreSubmissionScreen: Shows confirmation
        User->>ScoreSubmissionScreen: Confirms anonymous submission
        ScoreSubmissionScreen->>SpaceInvaders: Calls handleAnonymousSubmit()
        SpaceInvaders->>ConvexBackend: Calls saveScore(score, ..., isAnonymous: true)
        ConvexBackend->>ConvexBackend: saveScore() to DB
        ConvexBackend-->>SpaceInvaders: Returns success
    end