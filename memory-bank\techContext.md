# Technical Context: Blue Sky Invaders

## Technology Stack

### Frontend
- **Framework:** React with TypeScript
- **Bundler:** Vite
- **Styling:** Tailwind CSS
- **Canvas:** HTML5 Canvas for game rendering
- **Testing:** Built-in unit test support

### Backend
- **Platform:** Convex
- **Features:**
  - Authentication
  - Data storage
  - Real-time updates
  - Social integration

### Build & Development
- **Package Manager:** npm/yarn
- **Development Server:** Vite dev server
- **TypeScript Configuration:** Strict mode enabled

## Development Environment

### Required Tools
- Node.js
- npm or yarn
- Visual Studio Code (recommended)
- Git

### VS Code Extensions
- TypeScript support
- ESLint
- Prettier
- Tailwind CSS IntelliSense

### Project Configuration Files
- `tsconfig.json`: TypeScript configuration
- `vite.config.ts`: Vite bundler settings
- `tailwind.config.js`: Tailwind CSS configuration
- `eslint.config.js`: ESLint rules
- `components.json`: Component library configuration

## Architecture Overview

### Current File Structure (Post-Enemy System Extraction)
```
src/
├── SpaceInvaders.tsx (~1040 lines)          # Main component, state management, UI
├── SpaceInvadersGameLogic.tsx (~1050 lines) # Core game mechanics, collision detection
├── SpaceInvadersRenderer.tsx (~580 lines)   # Canvas rendering, visual effects
├── enemySystem.tsx (~330 lines)             # Enemy management, AI, spawning
├── gameLogic/
│   ├── difficultyManager.ts                 # Level progression, enemy scaling
│   ├── upgradeManager.ts                    # Power-up system, player upgrades
│   └── audioManager.ts                      # Sound effects, audio management
└── components/
    ├── gameUI/                              # UI components
    └── shared/                              # Reusable components
```

### Planned Future Extractions
```
src/
├── combatSystem.tsx (~200-300 lines)        # Bullet management, collision detection
├── playerSystem.tsx (~150-200 lines)        # Player movement, upgrades, health
├── gameEffects.tsx (~100-150 lines)         # Particle systems, visual effects
└── [existing files...]
```

## Technical Constraints

### Browser Support
- Modern browsers with Canvas API support
- Mobile browser compatibility
- Touch event handling

### Performance Requirements
- 60 FPS target
- Smooth animations
- Efficient collision detection
- Optimized rendering

### Mobile Considerations
- Touch input handling
- Screen size adaptation
- Battery usage optimization
- Performance on low-end devices

## Development Patterns

### Code Organization
```
project/
├── src/
│   ├── SpaceInvaders.tsx              # Main game component
│   ├── SpaceInvadersGameLogic.tsx     # Game logic & mechanics
│   ├── SpaceInvadersRenderer.tsx      # Rendering & visual effects
│   ├── components/
│   │   ├── gameUI/                    # Game-specific UI components
│   │   └── ui/                        # Reusable UI components
│   ├── gameLogic/                     # Specialized game systems
│   │   ├── asteroidManager.ts
│   │   ├── enemyManager.ts
│   │   └── powerUpManager.ts
│   ├── lib/
│   └── types/
├── public/
│   ├── audio/
│   └── assets/
└── convex/
    └── schema/
```

### TypeScript Practices
- Strict type checking
- Interface-first design
- Proper type exports
- Generic components

### Component Structure
- Functional components
- React hooks for state
- Props interface definitions
- Proper event typing

### Modular Architecture (Updated 2025-06-30)
#### Main Game Component (`SpaceInvaders.tsx`)
- **Purpose**: State management, UI rendering, event handling
- **Size**: ~1040 lines (reduced from ~1400)
- **Responsibilities**:
  - Game state management
  - User interface rendering
  - Input handling
  - Component lifecycle management

#### Game Logic Module (`SpaceInvadersGameLogic.tsx`)
- **Purpose**: Core game mechanics and logic
- **Size**: ~500 lines
- **Responsibilities**:
  - Game loop and updates
  - Collision detection
  - Entity management
  - Sound system integration
- **Pattern**: Dependency injection with `GameLogicDependencies` interface

#### Rendering Module (`SpaceInvadersRenderer.tsx`)
- **Purpose**: Canvas rendering and visual effects
- **Size**: ~580 lines
- **Responsibilities**:
  - Canvas drawing operations
  - Visual effects (CRT overlay, particles, explosions)
  - UI element rendering
  - Animation management
- **Pattern**: Custom hook pattern with `useRenderingHooks`

## Build & Deployment

### Development
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Type checking
npm run typecheck

# Linting
npm run lint
```

### Production
```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## Testing Strategy

### Unit Tests
- Component testing
- Game logic testing
- Utility function testing

### Integration Tests
- Game state transitions
- User interaction flows
- Social feature integration

### Performance Testing
- FPS monitoring
- Memory usage
- Network calls

## Asset Management

### Audio Files
- Background music tracks
- Sound effects
- Audio format optimization

### Game Assets
- Sprite management
- Asset preloading
- Caching strategy

## Security Considerations

### Authentication
- Convex authentication system
- Social login integration
- Session management

### Data Protection
- Score validation
- Anti-cheat measures
- User data privacy

## Monitoring & Debugging

### Development Tools
- Browser DevTools
- React DevTools
- Performance profiling

### Error Handling
- Error boundaries
- Logging system
- User feedback

## Documentation

### Code Documentation
- TSDoc comments
- README files
- Architecture diagrams
- Module-specific documentation

### API Documentation
- Endpoint descriptions
- Type definitions
- Usage examples

### Architecture Documentation (Updated 2025-06-30)
#### Development Guidelines for New Modular Structure

**Adding Game Logic Features:**
```typescript
// Add to SpaceInvadersGameLogic.tsx
export const createGameLogicHooks = (deps: GameLogicDependencies) => {
  // New game mechanics go here
  const newFeature = useCallback(() => {
    // Implementation
  }, [/* dependencies */]);

  return { /* existing functions */, newFeature };
};
```

**Adding Visual Effects:**
```typescript
// Add to SpaceInvadersRenderer.tsx
export const useRenderingHooks = (deps: RendererDependencies) => {
  // New rendering functions go here
  const newVisualEffect = useCallback(() => {
    // Canvas operations
  }, [/* dependencies */]);

  return { /* existing functions */, newVisualEffect };
};
```

**Integration Pattern:**
```typescript
// In SpaceInvaders.tsx
const gameLogic = createGameLogicHooks(gameLogicDeps);
const rendering = useRenderingHooks(renderingDeps);
const { newFeature } = gameLogic;
const { newVisualEffect } = rendering;
```

## Version Control

### Git Workflow
- Feature branches
- Pull request reviews
- Semantic versioning

### Release Process
- Version bumping
- Changelog updates
- Deploy verification
