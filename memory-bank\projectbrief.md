# Project Brief: Space Invaders Game

## Overview
A modern web-based implementation of the classic Space Invaders game, built with real-time multiplayer capabilities using Convex backend and React frontend.

## Core Requirements
1. Classic Space Invaders gameplay mechanics
2. Real-time multiplayer features
3. <PERSON><PERSON> handle verification for score submission
4. Sound effects and background music
5. High score tracking

## Technical Stack
- Frontend: React + TypeScript + Vite
- Backend: Convex
- External APIs: Bluesky API for handle verification
- Styling: Tailwind CSS
- Audio: Web Audio API
