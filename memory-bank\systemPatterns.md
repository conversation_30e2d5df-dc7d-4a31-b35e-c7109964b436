# System Patterns

## Architecture Overview

```mermaid
flowchart TD
    Client[React Client] -->|Game State| Convex[Convex Backend]
    Convex -->|Real-time Updates| Client
    Convex -->|Handle Verification| Bluesky[Bluesky API]
    
    subgraph Frontend
        React[React Components]
        UI[UI Components]
        Game[Game Engine]
        GameCanvas[GameCanvas]
        GameTypes[Game Types]
        Audio[Audio System]
        Score[Score System]
        Leaderboard[LeaderboardScreen]
        Background[AnimatedBackground]
        UpgradeMgr[UpgradeManager]
        UpgradeUI[UpgradeScreen]
        StreakBonus[Streak Bonus System]

        React --> Game
        Game --> GameCanvas
        Game --> GameTypes
        Game --> StreakBonus
        React --> Audio
        React --> Score
        React --> UI
        UI --> Background
        Score --> Leaderboard
        Game --> UpgradeMgr
        Game --> UpgradeUI
    end
    
    subgraph Backend
        GameState[Game State]
        ScoreSystem[Score System]
        BlueskyVerify[Handle Verification]
        TopRankAnnounce[Top Rank Announcements]
        GameState --> ScoreSystem
        ScoreSystem --> BlueskyVerify
        ScoreSystem --> TopRankAnnounce
        TopRankAnnounce --> Bluesky[Bluesky API]
    end
```

## Key Design Patterns

### Frontend Patterns
1. **Component Structure**
   - Main App container
   - Game component (SpaceInvaders) - container component managing game state and logic
   - GameCanvas component - presentation component handling all rendering
   - Score submission with Bluesky handle verification
   - LeaderboardScreen component with pagination (100 scores/page, 1000 total) and Bluesky handle search
   - Audio system components
   - Reusable UI components (AnimatedBackground for consistent visual effects)

2. **Container-Presentation Pattern**
   - **Container Components** (e.g., SpaceInvaders.tsx):
     - Manage application state and game logic
     - Handle user input and events
     - Coordinate with other components
     - Pass data to presentation components
   - **Presentation Components** (e.g., GameCanvas.tsx):
     - Receive data via props
     - Handle rendering and visual effects
     - Don't maintain game state
     - Focus on UI concerns
   - **Benefits**:
     - Separation of concerns
     - Improved testability
     - Better code organization
     - Easier maintenance and extension

3. **Game Engine**
   - Real-time game loop
   - Sprite management
   - Collision detection
   - Audio management
   - Type-safe game objects

4. **State Management**
   - Convex real-time queries and mutations
   - Local game state
   - Audio state
   - Paginated score state
   - Centralized type definitions in gameTypes.ts

5. **Upgrade System**
   - **`UpgradeManager`**: A stateful class that tracks which upgrades the player has acquired and their current levels. It is responsible for providing a random selection of 3 available upgrades when requested.
   - **`UpgradeScreen`**: A UI component that receives upgrade options and presents them to the player. It handles user input (keyboard/mouse) to select an upgrade.
   - **Data Flow**: When the player levels up, the main game component calls `getUpgradeOptions()` from the `UpgradeManager`. The returned options are passed as props to the `UpgradeScreen`. When the player selects an option, the `onSelect` callback is triggered, informing the game component which upgrade was chosen. The game component then calls the `levelUp()` method on the `UpgradeManager` to update the player's state.

6. **Streak-Based Score Bonus**
   - **State Tracking**: The `player` object within the main game state now includes a `killStreak` counter and an `isMaxStreakActive` boolean.
   - **Core Logic**: Integrated directly into the `updateGame` function. On each enemy kill, the `killStreak` is incremented. The function checks if a milestone (10, 25, 50) has been reached.
   - **Bonus Application**: A `streakBonus` is calculated based on the highest milestone achieved and added to the score for every kill while the streak is active.
   - **Visual Feedback**:
       - **Milestone Notification**: Pushes a `FloatingText` object with `isStreakBonus: true` to the game state, which triggers a special, large, animated rendering effect.
       - **Max Streak Effect**: Sets `isMaxStreakActive` to true, which is observed by a `useEffect` hook in the main `SpaceInvaders` component. This hook manages a state variable (`borderColor`) and an interval to make the game window's `boxShadow` flash.
   - **Reset Condition**: The `killStreak` and `isMaxStreakActive` flag are reset to their initial state whenever the player takes damage.

### Backend Patterns
1. **Score Submission System**
   - Bluesky handle verification
   - Score validation
   - High score tracking
   - Paginated score retrieval
   - Handle-based score search
   - Automated top-rank announcements

2. **Game State Management**
   - Player positions
   - Enemy formations
   - Projectile tracking
   - Score keeping

3. **Multiplayer Coordination**
   - State synchronization
   - Player interactions
   - Real-time updates

## Implementation Notes

### Game Logic
- Core game loop runs on the client
- Server validates important game events
- Real-time state synced through Convex

### Data Flow
1. **Game Progress**
   - User actions trigger local updates first
   - Critical events synchronized with server
   - State changes broadcast to other players

2. **Score Submission**
   - Game over triggers score submission flow
   - User enters Bluesky handle
   - Handle verified through Bluesky API
   - Score stored with verified handle or 'Anonymous'
   - Scores retrievable through paginated API
   - Search by Bluesky handle supported
   - Automated announcements for top 3 scores

### Audio System
- Preloaded sound effects
- Background music management
- Volume control and muting

### UI System
1. **Shared Visual Components**
   - `AnimatedBackground`: Global component for consistent visual effects
   - Canvas-based animation system with configurable parameters
   - Responsive design adapting to different screen sizes
   - Performance optimizations (cleanup on unmount, throttled resize events)

2. **Theming Strategy**
   - Consistent color scheme (primary: red theme)
   - Retro visual aesthetic
   - Configurable animation intensity and colors
   - Separation of game UI from background effects

3. **UI State Management**
   - Component-specific state
   - Shared global visuals
   - Game state-dependent UI transitions
