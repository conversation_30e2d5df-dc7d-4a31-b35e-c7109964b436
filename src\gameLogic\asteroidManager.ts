import { GameObject, Player, Bullet, Particle } from '../SpaceInvaders';

/**
 * Represents a single asteroid in the game.
 */
export interface Asteroid extends GameObject {
  health: number;
  maxHealth: number;
  shape: { x: number; y: number }[];
  isFading: boolean;
  fadeStartTime: number;
}

/**
 * Spawns a specified number of asteroids with a given speed.
 * @param count The number of asteroids to spawn.
 * @param canvasWidth The width of the game canvas.
 * @param speed The vertical speed for the asteroids.
 * @returns An array of new Asteroid objects.
 */
function createAsteroidWave(count: number, canvasWidth: number, speed: number): Asteroid[] {
  const asteroids: Asteroid[] = [];
  for (let i = 0; i < count; i++) {
    const size = 30 + Math.random() * 30;
    const health = 5000 + Math.random() * 5000;
    const newAsteroid: Asteroid = {
      x: Math.random() * (canvasWidth - size),
      y: -size - Math.random() * 200,
      width: size,
      height: size,
      vy: speed,
      health: health,
      maxHealth: health,
      shape: createJaggedShape(size),
      isFading: false,
      fadeStartTime: 0,
    };
    asteroids.push(newAsteroid);
  }
  return asteroids;
}

/**
 * Manages the spawning of both waves and solo asteroids based on timers and chance.
 * @param currentTime The current game time.
 * @param lastWaveSpawnTime The time the last wave was spawned.
 * @param lastSoloSpawnTime The time the last solo asteroid was spawned.
 * @param canvasWidth The width of the canvas.
 * @returns An object containing new asteroids and the updated spawn times.
 */
export function manageAsteroidSpawning(
  currentTime: number,
  lastWaveSpawnTime: number,
  lastSoloSpawnTime: number,
  canvasWidth: number
): { newAsteroids: Asteroid[]; newLastWaveSpawnTime: number; newLastSoloSpawnTime: number } {
  const newAsteroids: Asteroid[] = [];
  let newLastWaveSpawnTime = lastWaveSpawnTime;
  let newLastSoloSpawnTime = lastSoloSpawnTime;

  const BASE_ENEMY_SPEED = 1; // Assuming a base enemy speed for reference
  const waveSpawnInterval = 15000 + Math.random() * 5000; // 15-20 seconds

  // Wave spawn logic
  if (currentTime - lastWaveSpawnTime > waveSpawnInterval) {
    if (Math.random() < 0.30) { // 30% chance to spawn a wave
      const waveSize = 3 + Math.floor(Math.random() * 5); // 3 to 7 asteroids
      const waveSpeed = BASE_ENEMY_SPEED * (1.3 + Math.random() * 0.7);
      newAsteroids.push(...createAsteroidWave(waveSize, canvasWidth, waveSpeed));
    }
    newLastWaveSpawnTime = currentTime;
  }

  // Solo spawn logic
  if (currentTime - lastSoloSpawnTime > 5000) { // Attempt to spawn a solo one every 5s
    if (Math.random() < 0.1) { // 10% chance for a solo asteroid
        const soloSpeed = BASE_ENEMY_SPEED * (1.3 + Math.random() * 0.7);
        newAsteroids.push(...createAsteroidWave(1, canvasWidth, soloSpeed));
    }
    newLastSoloSpawnTime = currentTime;
  }

  return { newAsteroids, newLastWaveSpawnTime, newLastSoloSpawnTime };
}


/**
 * Updates the position and state of all asteroids, including fade-out logic.
 * @param asteroids The array of asteroids from the game state, which will be mutated.
 * @param gameSpeed The current speed multiplier of the game.
 * @param currentTime The current game time.
 */
export function updateAsteroids(asteroids: Asteroid[], gameSpeed: number, currentTime: number): void {
  for (let i = asteroids.length - 1; i >= 0; i--) {
    const asteroid = asteroids[i];
    if (asteroid.isFading) {
      if (currentTime - asteroid.fadeStartTime > 200) { // 200ms fade out
        asteroids.splice(i, 1);
      }
    } else {
      asteroid.y += (asteroid.vy ?? 0) * gameSpeed;
    }
  }
}

// Simple AABB collision detection
function checkCollision(a: GameObject, b: GameObject): boolean {
  return a.x < b.x + b.width && a.x + a.width > b.x && a.y < b.y + b.height && a.y + a.height > b.y;
}

export function handleAsteroidCollisions(
  asteroids: Asteroid[],
  player: Player,
  bullets: Bullet[],
  createParticles: (x: number, y: number, count: number, color: string) => void,
  playSound: (sound: string) => void,
  currentTime: number
): { playerHealthLost: number; bulletsToRemove: Set<Bullet> } {
  const result = {
    playerHealthLost: 0,
    bulletsToRemove: new Set<Bullet>(),
  };

  asteroids.forEach((asteroid) => {
    if (asteroid.isFading) return;

    // Player bullets vs Asteroids
    bullets.forEach((bullet) => {
      if (bullet.isPlayerBullet && !result.bulletsToRemove.has(bullet) && checkCollision(bullet, asteroid)) {
        asteroid.health -= bullet.damage;
        result.bulletsToRemove.add(bullet);
        createParticles(bullet.x, bullet.y, 3, '#cccccc');
        if (asteroid.health <= 0) {
          asteroid.isFading = true;
          asteroid.fadeStartTime = currentTime;
          createParticles(asteroid.x + asteroid.width / 2, asteroid.y + asteroid.height / 2, 25, '#A9A9A9');
          playSound('enemyKill');
        } else {
          playSound('enemyHit');
        }
      }
    });

    // Enemy bullets vs Asteroids
    bullets.forEach((bullet) => {
      if (!bullet.isPlayerBullet && !result.bulletsToRemove.has(bullet) && checkCollision(bullet, asteroid)) {
        result.bulletsToRemove.add(bullet);
        createParticles(bullet.x, bullet.y, 2, '#ff8888');
      }
    });

    // Player vs Asteroids
    if (checkCollision(player, asteroid)) {
      result.playerHealthLost += 10; // Updated damage
      asteroid.isFading = true; // Start fading instead of instant destruction
      asteroid.fadeStartTime = currentTime;
    }
  });

  return result;
}

/**
 * Generates a random, jagged shape for an asteroid.
 * @param size The overall size of the asteroid.
 * @returns An array of points {x, y} relative to the asteroid's center.
 */
function createJaggedShape(size: number): { x: number; y: number }[] {
  const points: { x: number; y: number }[] = [];
  const numVertices = 8 + Math.floor(Math.random() * 5); // 8 to 12 vertices
  const radius = size / 2;

  for (let i = 0; i < numVertices; i++) {
    const angle = (i / numVertices) * 2 * Math.PI;
    const randomRadius = radius * (0.8 + Math.random() * 0.4); // Varying radius for jaggedness
    points.push({
      x: Math.cos(angle) * randomRadius,
      y: Math.sin(angle) * randomRadius,
    });
  }
  return points;
}