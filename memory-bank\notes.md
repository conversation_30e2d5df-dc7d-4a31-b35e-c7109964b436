## Test mode
- Start the game (optionally use `window.devForceStartGame()` to bypass the menu).

- In the browser console, type `window.enableGodMode()` to activate. Player health will reset on death.

- Type `window.disableGodMode()` to deactivate. Normal death rules will apply.


- `window.devForceStartGame()` and press Enter.

  - The page will reload.
  - The game should now start directly in the 'playing' state.

- To revert to the normal behavior (starting with the menu), type `window.devClearForceStartGame()` in the console and press Enter.

## Player Ship Morphing Feature Plan (Session: 2025-06-11)

__Goal:__ Implement a player ship that visually changes (morphs) based on its `powerLevel` and `fireRate` attributes to provide visual feedback for upgrades.

__Original Implementation Plan:__

1. __Create a New Component File:__

   - File Path: `src/components/gameUI/playerShip.tsx`

2. __Define `drawPlayerShip` Function in the New File:__

   - Signature: `function drawPlayerShip(ctx: CanvasRenderingContext2D, player: Player, determinedPlayerColor: string, determinedPlayerInnerColor: string, canvasScale: number): void`

   - __Base Shape:__ Draw the ship as a main triangle (body) and a smaller inner triangle (cockpit/detail).

   - __Power Level Morphing Logic (`player.powerLevel`):__

     - `powerLevel >= 2`: Add triangular wing extensions to the sides.
     - `powerLevel >= 4`: Enlarge wing extensions, make them more swept-back. Add small rectangular "cannons" to the wingtips.
     - `powerLevel >= 5`: Slightly increase the size of the "cannons".

   - __Fire Rate Morphing Logic (`player.fireRate`):__

     - `fireRate <= 200ms`: Add a triangular engine flare at the rear center of the ship (e.g., semi-transparent orange).
     - `fireRate <= 120ms`: Make the engine flare larger, more intense in color. The flare's length should also scale slightly with `player.powerLevel`.

   - __Scaling:__ All coordinates and dimensions within `drawPlayerShip` must be multiplied by `canvasScale`.

   - Use `ctx.save()` and `ctx.restore()` to encapsulate drawing operations.

3. __Modify `src/SpaceInvaders.tsx`:__

   - __Export Interfaces:__ Add `export` keyword to `GameObject` and `Player` interfaces to make them accessible.

   - __Import:__ Import the `drawPlayerShip` function from `src/components/gameUI/playerShip.tsx`.

   - __Update `render` Function:__

     - Remove the existing `ctx.fillRect` calls used for drawing the player.
     - Call the new `drawPlayerShip` function, passing the canvas context, player object, determined player colors, and `canvasScale`.


I have now read both `src/SpaceInvaders.tsx` and `src/gameLogic/difficultyManager.ts`.
I will synthesize the game rules based on the content of these files.

**Game Rules Summary:**

**1. Game Objective & Core Loop:**
   - Survive waves of enemies, shoot them down, and achieve a high score.
   - The game difficulty scales to player level.
   - The game ends when player health reaches 0

**2. Player:**
   - **Initial State:**
     - Health: 100 (maxHealth: 100)
     - Fire Rate: 300ms
     - Power Level: 1 (derived from `activeMultiShotLevel` which starts at 0)
     - Auto Heal Charges: 0
     - Dimensions: Width 50, Height 30
   - **Movement:**
     - Controlled by arrow keys or WASD (configurable in settings).
     - Mobile: Touch controls for left/right movement.
     - Speed: `PLAYER_SPEED` (constant value of 8).
   - **Shooting:**
     - Desktop: Space bar (or configured shoot key).
     - Mobile: Auto-fires.
     - Fire rate can be improved by power-ups (decreases `player.fireRate` value, min 50ms).
     - `player.powerLevel` (1 + `activeMultiShotLevel`) determines the number of bullets fired simultaneously.
       - Bullets spread out horizontally based on `powerLevel`.
     - Player bullet damage: 1.
     - Player bullet speed: Base -12 (upwards), modified by `settings.playerBulletSpeed`.
   - **Health & Damage:**
     - Takes damage from enemy bullets.
     - Dies if health <= 0 (unless God Mode is on, then health resets to max).
     - Can heal via 'health' power-up (+30 health, up to maxHealth).
     - Can use 'Auto Heal Charges' (press 'H' key, not on mobile) to heal 50 health if charges > 0 and health < maxHealth. Max 3 charges.
   - **Leveling Up:**
     - Levels up based on `enemiesKilled` and `currentLevel` (logic in `difficultyManager.ts: checkPlayerLevelUp`).
       - `enemiesKilled % (currentLevel * 15 + 15) === 0`.
     - Gains +10 health on level up (logic in `difficultyManager.ts: getPlayerHealthBoostOnLevelUp`), up to `maxHealth`.
   - **Power-ups:**
     - **Multi-Shot:** Increases `activeMultiShotLevel` by 1 (max 5). Each level lasts 10 seconds, timer resets on new pickup. `player.powerLevel` is `1 + activeMultiShotLevel`.
     - **Fire Rate:** Decreases `player.fireRate` by 30 (min 50ms).
     - **Health:** Increases health by 30 (up to `maxHealth`).
     - Power-ups drop from enemies with a 15% chance.
   - **Visuals:**
     - Ship color changes based on `powerLevel`.
     - Ship morphs (visual details change) based on `powerLevel` and `fireRate` (as implemented in `playerShip.tsx`).

**3. Enemies:**
   - **Types & Stats (from `createEnemy`):**
     - **Basic:** Health 1, Points 10, Fire Rate 2000ms, Width 30, Height 20.
     - **Fast:** Health 1, Points 20, Fire Rate 1500ms, Width 25, Height 15.
     - **Heavy:** Health 6 (3*2), Points 50, Fire Rate 3000ms, Width 40, Height 30.
     - **Boss:** Health 100 + `currentLevel`, Points 200, Fire Rate 800ms, Width 60, Height 40.
   - **Spawning:**
     - Spawn rate: `state.enemySpawnRate` (initially 2000ms, decreases with level: `max(800, 2000 - level * 100)`), modified by `settings.enemySpawnRate`.
     - Number of enemies per wave: `getEnemySpawnCount(level)` from `difficultyManager.ts` (`Math.min(3 + Math.floor(level / 5), 12)`).
     - Basic enemies spawn by default.
     - Fast enemies can spawn after level 8 (50% chance if basic not chosen).
     - Heavy enemies can spawn after level 14 (30% chance if basic/fast not chosen).
   - **Movement & Behavior:**
     - Move downwards (`vy`) and horizontally (`vx`), reversing `vx` at canvas edges.
     - Stop descending when they reach the `barrierLine`.
   - **Shooting:**
     - Regular enemies have a low chance (0.5%) to shoot if `currentTime - lastShot > fireRate`.
     - Enemy bullet damage: 10.
     - Enemy bullet speed: Base 6 (downwards), modified by `settings.enemyBulletSpeed`.
   - **Boss Mechanics:**
     - Spawns from level 20 onwards, with increasing probability within 10-level blocks (guaranteed at level 29, 39, etc.).
     - Only one boss active at a time.
     - Boss `powerLevel` increases with each boss spawn (`globalBossPowerLevelRef`).
     - Reduces regular enemy spawn count by 70% while active.
     - Alternates attack patterns ('multiSpread', 'focusedBarrage') every 8 seconds.
     - Telegraphs pattern switches for 1 second.
     - **MultiSpread:** Fires `powerLevel` bullets spread out. Damage 15. Speed 5.
     - **FocusedBarrage:** Fires `powerLevel` bullets in a tighter spread. Damage 12. Speed 7.
     - Defeating a boss increments the `barrierLine` (pushes it down).

**4. Barrier Line:**
   - A horizontal line that enemies cannot cross once they reach it.
   - Starts at `barrierLine = 6` (out of 12 divisions of canvas height).
   - Can be pushed down (value decreases, max 0) by collecting 'barrierBoost' crates.
   - Pushed up (value increases, max 11) when a boss is defeated.

**5. Special Ships & Crates:**
   - **Special Ship:**
     - Spawns randomly carrying either 'barrierBoost' or 'autoHeal'.
     - Flies horizontally across the screen.
     - Health: 1. Must be shot to drop its crate.
     - Drops crate after a 500ms delay once health is 0.
     - **Barrier Boost Ship:** Spawns within levels 45-50 of every 50-level block.
     - **Auto Heal Ship:** Spawns within levels 75-85 of every 80-level block (if barrier boost didn't spawn).
   - **Crate:**
     - Falls vertically.
     - Collected by player collision.
     - **Barrier Boost Crate:** Pushes `barrierLine` down by 5 units (making it harder for enemies).
     - **Auto Heal Crate:** Grants 1 `autoHealCharge` to the player (max 3).

**6. Scoring & Game State:**
   - Score increases by enemy points when an enemy is killed.
   - Game states: 'menu', 'playing', 'gameOver', 'options', 'submitScore', 'leaderboard'.
   - Score can be submitted to a leaderboard (optionally with a Bluesky handle).

**7. Settings (affect gameplay):**
   - `enemySpawnRate`: Multiplier for enemy spawn interval.
   - `playerFireRate`: Multiplier for player fire interval.
   - `enemyBulletSpeed`: Multiplier for enemy bullet speed.
   - `playerBulletSpeed`: Multiplier for player bullet speed.
   - Audio settings (volume, enabled/disabled).
   - Control scheme ('arrows' or 'wasd').

**8. God Mode (Dev Feature):**
   - If active, player health resets to max upon "death" instead of game over.

This covers the main rules and mechanics derived from the provided files.
I have analyzed `src/SpaceInvaders.tsx` and `src/gameLogic/difficultyManager.ts`. Here's a summary of the game rules I've learned:

**I. Game Core & Objective:**
*   **Goal:** Survive waves of attacking enemies, shoot them down, achieve a high score, and progress through levels.
*   **Game Over:** Occurs when the player's health drops to 0, unless "God Mode" is enabled (in which case health is restored).
*   **Game States:** The game transitions through states like 'menu', 'playing', 'gameOver', 'options', 'submitScore', and 'leaderboard'.

**II. Player Mechanics:**
*   **Initial Stats:** Starts with 100 health (max 100), a fire rate of 300ms, power level 1, and 0 auto-heal charges. Ship dimensions are 50x30.
*   **Controls:**
    *   Desktop: Arrow keys or WASD for movement (configurable). Configured key (default Space) for shooting.
    *   Mobile: Touch controls for left/right movement; auto-fires.
    *   Movement Speed: Constant (PLAYER_SPEED = 8).
*   **Shooting:**
    *   Fire rate is influenced by `player.fireRate` (lower is faster, min 50ms) and `settings.playerFireRate` multiplier.
    *   `player.powerLevel` (derived from `1 + activeMultiShotLevel`) determines the number of bullets fired simultaneously, spreading horizontally.
    *   Player bullets deal 1 damage. Speed is -12 (upwards), modified by `settings.playerBulletSpeed`.
*   **Health & Damage:**
    *   Loses health from enemy bullets.
    - Can heal via 'health' power-ups (+30 health, up to max).
    *   Can use 'Auto Heal Charges' by pressing 'H' (desktop only) to restore 50 health if charges are available (max 3 charges).
*   **Leveling Up (`difficultyManager.ts`):**
    *   Condition: `enemiesKilled % (currentLevel * 15 + 15) === 0`.
    *   Reward: +10 health (up to max health).
*   **Power-ups (15% drop chance from enemies):**
    *   **Health:** +30 health.
    *   **Fire Rate:** Reduces `player.fireRate` by 30ms (making it faster).
    *   **Multi-Shot:** Increases `activeMultiShotLevel` by 1 (max 5). Each level lasts 10 seconds; timer resets on pickup. This directly increases `player.powerLevel`.
*   **Visuals:** Ship color and appearance (morphing) change based on `powerLevel` and `fireRate`.

**III. Enemy Mechanics:**
*   **Types & Base Stats:**
    *   **Basic:** 1 HP, 10 pts, 2000ms fire rate.
    *   **Fast:** 1 HP, 20 pts, 1500ms fire rate.
    *   **Heavy:** 6 HP, 50 pts, 3000ms fire rate.
    *   **Boss:** (100 + currentLevel) HP, 200 pts, 800ms fire rate.
*   **Spawning:**
    *   Spawn interval: Starts at 2000ms, decreases with level (`max(800, 2000 - level * 100)`), affected by `settings.enemySpawnRate`.
    *   Number per wave (`difficultyManager.ts`): `Math.min(3 + Math.floor(currentLevel / 5), 12)`.
    *   Fast enemies appear after level 8; Heavy enemies after level 14.
*   **Behavior:**
    *   Move downwards and horizontally, reversing direction at screen edges.
    *   Stop descending upon reaching the `barrierLine`.
*   **Shooting:**
    *   Regular enemies have a very low (0.5%) chance to shoot if their fire rate cooldown is met.
    *   Enemy bullets deal 10 damage. Speed is 6 (downwards), modified by `settings.enemyBulletSpeed`.
*   **Boss Mechanics:**
    *   First eligible spawn from level 20. Spawn chance increases within 10-level blocks, guaranteed at the end of each block (e.g., level 29, 39).
    *   Only one boss active. Reduces regular enemy spawns by 70%.
    *   Boss `powerLevel` increases with each new boss.
    *   Alternates attack patterns ('multiSpread' vs 'focusedBarrage') every 8 seconds, telegraphed for 1 second.
        *   MultiSpread: `powerLevel` bullets, 15 damage, speed 5.
        *   FocusedBarrage: `powerLevel` bullets (tighter), 12 damage, speed 7.
    *   Defeating a boss pushes the `barrierLine` upwards (value increases).

**IV. Special Game Elements:**
*   **Barrier Line:**
    *   A defensive line enemies cannot cross. Starts at `barrierLine = 6` (relative to screen height divisions).
    *   Can be pushed down (value decreases, max 0, beneficial to player) by 'Barrier Boost' crates.
    *   Pushed up (value increases, max 11, detrimental) when a boss is defeated.
*   **Special Ships & Crates:**
    *   **Special Ship:** Flies horizontally. Health: 1. Must be shot to drop a crate (after a 500ms delay).
        *   'Barrier Boost' Ship: Spawns between levels 45-50 of every 50-level block.
        *   'Auto Heal' Ship: Spawns between levels 75-85 of every 80-level block (if Barrier Boost didn't spawn).
    *   **Crates:** Fall vertically. Collected on player collision.
        *   'Barrier Boost' Crate: Pushes `barrierLine` down by 5 units.
        *   'Auto Heal' Crate: Grants 1 `autoHealCharge` (max 3).

**V. Scoring & Progression:**
*   Score is awarded for defeating enemies based on their point value.
*   The game tracks `score`, `level`, and `enemiesKilled`.
*   Scores can be submitted to a leaderboard.

**VI. Game Settings (Modifiable by Player):**
*   `enemySpawnRate`, `playerFireRate`, `enemyBulletSpeed`, `playerBulletSpeed` (multipliers).
*   Music & SFX volume and enabled/disabled state.
*   Control scheme (arrows vs WASD).

**VII. Development Features:**
*   **God Mode:** Prevents game over by restoring player health on "death". Activated via console.

This summary should cover the essential rules and mechanics of the game as defined in the code.


# Space Invaders Game Development Summary (Recent Session)

This session focused on several game balance adjustments, bug fixes, and new feature implementations for the Space Invaders game.

## Key Changes and Features:

1. __Enemy Spawning Adjustments:__

   - __Minimum Spawn Interval Reduced:__ The minimum time between enemy waves was decreased from 800ms to __600ms__ to increase late-game intensity.

     - File: `src/SpaceInvaders.tsx`
     - Code: `state.enemySpawnRate = Math.max(600, 2000 - level * 100);`

   - __Maximum Regular Enemy Cap Increased:__ The cap for regular enemies spawning per wave was raised from 12 to __25__.

     - File: `src/gameLogic/difficultyManager.ts`
     - Function: `getEnemySpawnCount`
     - Code: `return Math.min(3 + Math.floor(currentLevel / 5), 25);`

2. __Diagonal Enemy Bullets (New Feature & Bug Fix):__

   - __Functionality:__ Fast and Heavy type enemies, from level 21 onwards, now have a chance to fire bullets diagonally (30 degrees down-left or down-right).
     - The probability is a 50% chance to attempt a shot when ready, and if so, a 15% chance for that shot to be diagonal (effective 7.5% chance for a diagonal shot per firing opportunity for eligible enemies).

   - __Implementation Details:__

     - Added `vx` (horizontal velocity) property to the `Bullet` and `GameObject` interfaces.
     - Updated enemy shooting logic in `src/SpaceInvaders.tsx` to calculate `vx` and `vy` for diagonal shots.
     - __Critical Fix:__ Corrected the main bullet movement loop in `updateGame` to use `vx` for horizontal movement (`state.bullets.forEach((b: Bullet) => { b.x += b.vx || 0; b.y += b.vy || 0; });`). This was a key step after several debugging attempts where `vx` was being set but not applied.

   - __Debugging:__ Added `console.log` statements to trace the logic for diagonal bullet firing. These logs are currently still in the code in `src/SpaceInvaders.tsx`.

3. __Points for Power-Up Collection (New Feature):__

   - Players now receive __+5 points__ to their score for each power-up they collect.
   - File: `src/SpaceInvaders.tsx`
   - Implemented by adding `setScore(s => s + 5);` within the power-up collection logic in the `updateGame` function.


1. "Echo" Projectiles (Offensive):
Concept: A chance for player projectiles to leave behind a temporary "echo" that lingers briefly (e.g., 0.5 seconds) and damages enemies passing through it.
Implementation:
Add an "echoChance" stat to the player (or an upgrade).
On projectile fire, calculate a random number. If within the echoChance, create an "echo" object in the game state with:
Position of the projectile.
A timer for its duration.
Low damage value.
In the updateGame loop, update the echo's timer and handle collisions between echos and enemies.
Render the echo as a semi-transparent version of the original projectile.
Player Satisfaction: Provides persistent damage in areas, good for controlling waves, and a unique visual flair.
Synergies: Good with multi-shot, explosive johnny.
2. "Momentum Shield" (Defensive):
Concept: Every time the player moves a significant distance, a temporary shield is briefly generated in that direction of movement, blocking projectiles.
Implementation:
Track player's position each frame.
Calculate distance moved since the last shield generation.
If distance exceeds a threshold, create a "shield" object:
Positioned in the direction of movement (e.g., left of the player if moving left).
Has a small HP value (blocks a few shots).
A timer for its duration.
Handle collisions between the shield and enemy projectiles.
Player Satisfaction: Rewards active dodging and movement, turning skillful play into a defensive advantage.
Synergies: Good for mobile players, risky gameplay.

3. "Tactical Retreat" Power-Up (Utility):
Concept: A single-use power-up that instantly teleports the player a short distance backwards, creating a brief explosion at their original location.
Implementation:
Add a "tacticalRetreat" boolean to the player state (starts as false).
When the power-up is collected, set tacticalRetreat = true;.
Add a keybind handler to check for the "retreat" key (e.g., 'R'). When pressed and tacticalRetreat is true:
Set tacticalRetreat = false;
Teleport the player (adjust x,y coords) backwards a fixed distance.
Create an explosion animation at the original player location (using existing explosion logic).
Player Satisfaction: A defensive "get out of jail free" card that requires quick thinking.
Synergies: Great with "Sol Invictus" for survival, risky gameplay.

4. "Parasitic Shot" Upgrade (Offensive):
Concept: A chance for player projectiles to "infect" enemies. Infected enemies take damage over time and pass the infection to nearby enemies upon death.
Implementation:
Add an "infectChance" stat (or upgrade).
On projectile hit, calculate a random number.
If within the infectChance, set an isInfected: true flag on the enemy.
Add infectionDamage value and timer.
In the updateGame loop:
If isInfected: true on an enemy, apply damage over time.
On death of an infected enemy, create small infectChance to nearby enemies.
Player Satisfaction: Creates chain reactions of damage, rewarding careful targeting and area control.
Synergies: good with multi-shot, aoe dmg.

5. "Gravitational Anomaly" Environmental Hazard:
Concept: Periodically, the game spawns small "gravity wells" that attract nearby enemies and projectiles, creating chaotic formations.
Implementation:
Add a gravityWells: GravityWell[] array to the game state.
Spawn gravity wells periodically with:
Position.
A "pull strength".
A duration.
In the updateGame loop:
For each gravity well, calculate the distance to each enemy and projectile.
Apply a force pulling them towards the gravity well (force strength based on the pull strength and distance).
Player Satisfaction: Creates unpredictable situations and rewards adaptive play.
Synergies: aoe damage

6. "Friendly Fire" Elite Variant (Enemy Modification):
Concept: A rare elite enemy type that has a chance to fire projectiles that damage other enemies in addition to the player.
Implementation:
When spawning enemies, give some a chance to have an isFriendlyFire: true flag.
When this enemy shoots, change the collision detection: If the bullet collides with another enemy and it is still active, deal the enemy the same dmg.
Player Satisfaction: A unique twist on enemy behavior that can create chain reactions of destruction, while also presenting a unique threat.
Synergies:

7. "Temporal Distortion" Power-Up (Tempo Control):
Concept: A power-up that briefly slows down enemy projectiles but increases enemy movement speed.
Implementation:
Add a temporalDistortionActive: boolean to the game state.
On power-up pickup, set temporalDistortionActive = true; for a duration.
In the updateGame loop:
If temporalDistortionActive, reduce the vy of enemy projectiles.
Increase the vy of enemies.

Player Satisfaction: A high-risk, high-reward power-up that rewards precise dodging and target prioritization.

8. "Mirror Shield" Power-Up (Defensive):
Concept: A circular shield that reflects enemy projectiles back towards enemies. Projectiles keep bouncing until they hit a wall, hit a player or disapear after a certain time.
Implementation:
Add shieldRadius to player statistics, if player gets a shield: spawn it with radius. When bullet collides shield: invert its velocity vector.
Player Satisfaction: Defensive option.

9. "Weapon Overheat" Mechanic (Limitation):
Concept: Limit shooting fire by making fire button generate heat, when the limit is reached, player is disabled and cannot fire, only move.
Implementation:
Add heat attribute to player. Every shoot heat +1, when maxHeat is reached player cannot fire.
Player Satisfaction: More complex gameplay, new strategy

10. Seasonal/Themed Modifiers (Community Engagement):
Concept: Implement seasonal or themed modifiers that alter game rules for a limited time, such as:
"Bullet Hell Season": Increased bullet speed and frequency.
"Melee Mayhem": Players gain bonus damage for colliding with enemies.
"One-Shot Wonder": Players have only 1 health, but deal massive damage.
Implementation:
Define a set of modifiers that can be activated/deactivated via a configuration file.
In the game logic, check which modifiers are active and adjust game parameters accordingly.
Promote the event on your social platform.
Player Satisfaction: Keeps the game fresh and engaging, encouraging players to return and compete on leaderboards with unique rules.
Synergies: Themed cosmetics tied to the event.


#### 1. "Temporal Echo" Upgrade (Time Manipulation):
Concept: A chance for a player's shot to create a brief "temporal echo" of the enemy it hits, causing it to briefly revert to a previous position along its path and repeat its last firing action.
Implementation:
Add a temporalEchoChance stat to the player.
When a projectile hits an enemy, generate a random number. If within temporalEchoChance:
Store the enemy's previous position (e.g., one second ago) and firing state (if applicable).
Create a "temporal echo" object that mirrors the enemy at that location, plays its last animation, and fires a mirrored projectile.
Give the echo a short lifespan, then remove it.
Player Satisfaction: This can create beautiful and chaotic moments, with enemies briefly repeating their actions, creating a satisfying visual and strategic layer.
Synergies: Highly effective with Multi-Shot, amplifying the chaos.
#### 2. "Adaptive Armor" Power-Up (Dynamic Defense):
Concept: A temporary shield that analyzes incoming enemy bullet types and automatically adjusts its resistance to match, providing significantly reduced damage from that specific bullet type.
Implementation:
When the power-up is activated:
Detect the type of enemy projectile currently closest to the player.
Create a shield object that grants heavy resistance to that specific projectile's damage type.
After the power-up duration ends, the shield disappears. If multiple projectile types are closeby use most commong kind.
Player Satisfaction: Encourages tactical observation of enemies and creates a feeling of proactive defense.
### 3. "Kinetic Transfer" Upgrade (Damage Amplification):
Concept: A percentage of the damage absorbed by the player is converted into bonus damage for the next few player shots.
Implementation:
Track damage taken by the player.
Store a percentage of that damage as "kinetic energy."
For the next N shots fired by the player, add this kinetic energy as bonus damage. Decay this value over time (like 1% a shot) if not used to prevent exploits.
Player Satisfaction: Promotes a risk-reward playstyle, turning damage taken into an offensive advantage.
### 4. "Mimic Drone" Power-Up (AI Companion Variation):
Concept: A single-use power-up that spawns a drone that temporarily mimics the firing behavior of a random enemy on screen.
Implementation:
On power-up activation:
Select a random enemy on the screen.
Create a drone object that fires the same projectile type with the same timing as that enemy.
Give the drone a limited lifespan.
Player Satisfaction: Creates unpredictable situations and rewards tactical use of the drone to target specific enemy types.
Synergies:
### 5. "Critical Feedback" System (Chance Based Damage):
Concept: Add a small chance for enemies hit by the player's bullets to have the amount of damage they deal amplified back at them for a short period of time.
Implementation:
Add feedbackChance to player statistics and if an enemy is shot, assign them a feedbackAmplifier
Player Satisfaction: Good DPS, and can be made in some funny visuals.
### 6. "Spatial Rift" Environmental Hazard (Dynamic Obstacle):
Concept: Periodically, rifts open in the game space, creating temporary loops or distortions in the projectile paths. Projectiles can enter one side of the rift and exit from another location, unpredictably changing the flow of battle.
Implementation:
Add a rifts: Rift[] array to the game state.
Spawn rifts periodically, each with:
An entrance position.
An exit position.
A size.
In the updateGame loop, if a projectile enters the area of a rift's entrance, teleport it to the exit position with adjusted velocity and direction.
Player Satisfaction: This adds a significant layer of dynamic challenge and strategic positioning beyond just dodging enemies and their bullets.
#### 7. "Convergence Field" Power-Up (Crowd Control):
Concept: Creates a localized field that attracts enemy bullets, pulling them into a central point. At the end of the duration, the gathered projectiles detonate.
Implementation:
When activated, creates a "convergence field" object.
In updateGame, enemy bullets get pulled towards convergence field object.
Player Satisfaction: A powerful crowd control mechanic that allows players to set up devastating detonations.
### 8. "Time Slip" Temporary Debuff (Disorientation):
Concept: On random occasions a debuff appears that causes the player to teleport forward or backward a bit, with the camera being fixed in place, to create an illusion.
Implementation:
Set a random timer for events to take place. When it happens, move the player forwards or backwards.
Player Satisfaction: High risk-reward gameplay option, but in the end should have some sort of a unique mechanic with it, maybe player deals more damage when having the de-buff.
### 9. "Weapon Sync" Mechanic:
Concept: Create a system of a combo of weapons with strengths and weaknesses. Then make combinations of these weaapons provide different stats and perks. The implementation would be a set number of stats when choosing an upgrade, depending on a weapon combo.
Implementation:
Create enum that has multiple weapons to choose from.
Create enum or class with stats to provide to chosen weapons and implement functionality.
Player Satisfaction: New experience
### 10. "Anomalous Enemy" AI Behavior (Emergent Gameplay):
Concept: At random intervals, especially in longer runs, the game could trigger a short, unexpected "Anomalous Event" that drastically alters enemy behavior for a limited time (e.g., 10-20 seconds). Examples include:
* "Enemy Swap": Enemies swap firing patterns.
* "Pacifist mode": Enemies dont fire, run faster.
Implementation:
Define a set of Anamalous enemies to spawn. When spawning, calculate if enemy can spawn and apply its characteristics.  