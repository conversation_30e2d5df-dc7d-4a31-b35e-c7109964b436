import React, { useState, useEffect } from 'react';
import { useAction } from 'convex/react';
import { api } from '../convex/_generated/api';
import { Id } from '../convex/_generated/dataModel';
import { victoryArt } from './lib/asciiArt';

interface Props {
  scoreId: Id<'scores'>;
  blueskyHandle: string;
  score: number;
  playerLevel?: number;
  enemiesKilled?: number;
  leaderboardPosition?: number;
  gameDuration?: string; // e.g., "5m 30s"
  onClose: () => void;
}

export default function BlueskyShare({
  scoreId,
  blueskyHandle,
  score,
  playerLevel,
  enemiesKilled,
  leaderboardPosition,
  gameDuration,
  onClose,
}: Props) {
  const [handle1, setHandle1] = useState('');
  const [handle2, setHandle2] = useState('');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPosting, setIsPosting] = useState(false);

  const postScoreToBluesky = useAction(api.social.postScore);
  const validateChallengeHandles = useAction(api.social.validateChallengeHandles);
  const challengePlayersAction = useAction(api.index.challengePlayers);

  const handlePostToBluesky = async () => {
    setIsSubmitting(true);
    setIsPosting(true);
    setError('');
    try {
      await postScoreToBluesky({ scoreId });
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Failed to post score to Bluesky');
    } finally {
      setIsSubmitting(false);
      setIsPosting(false);
    }
  };

  const handleChallengeSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const handlesToChallenge = [handle1, handle2].filter(h => h.trim() !== '');
    if (handlesToChallenge.length === 0) {
      setError('Please enter at least one Bluesky handle to challenge.');
      return;
    }
    setIsSubmitting(true);
    setError('');
    try {
      await validateChallengeHandles({ challengedHandles: handlesToChallenge });
      await challengePlayersAction({
        scoreId,
        challengedHandles: handlesToChallenge,
      });
      onClose(); 
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to challenge players. Please check handles and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const StatItem: React.FC<{ label: string; value: string | number | undefined; unit?: string }> = ({ label, value, unit }) => (
    <div className="flex justify-between items-center py-0.5 sm:py-1">
      <p className="text-xs text-red-300">{label}:</p>
      <p className="text-xs font-semibold text-red-100">
        {value !== undefined ? value : 'N/A'} {unit}
      </p>
    </div>
  );

  const ProgressBar: React.FC<{ label: string; value: string | number; percentage: number; unit?: string; animationDelay?: number }> = ({ label, value, percentage, unit, animationDelay = 100 }) => {
    const [currentWidth, setCurrentWidth] = useState(0);

    useEffect(() => {
      // Trigger the animation shortly after mount
      const timer = setTimeout(() => {
        setCurrentWidth(percentage);
      }, animationDelay);

      return () => clearTimeout(timer);
    }, [percentage, animationDelay]);

    return (
      <div className="mb-1 sm:mb-1.5">
        <div className="flex justify-between mb-0.5">
          <span className="text-xs font-medium text-red-300">{label}: {value}{unit ? ` ${unit}` : ''}</span>
          {/* Percentage display can be removed if the bar visually represents 100% fill based on value, or kept if meaningful */}
          {/* <span className="text-xs font-medium text-red-300">{percentage}%</span> */}
        </div>
        <div className="w-full bg-gray-700 rounded-full h-1 sm:h-1.5">
          <div
            className="bg-[var(--red-main-ff0000)] h-1 sm:h-1.5 rounded-full transition-all duration-1000 ease-out" // Increased duration for visibility
            style={{ width: `${currentWidth}%` }}
          ></div>
        </div>
      </div>
    );
  };
  return (
    // Ensure this outer div allows its child to take full height and provides scrolling if needed.
    // On mobile, it's a flex column. On sm+, it centers its child.
    <div className="fixed inset-0 bg-black/90 flex flex-col sm:items-center sm:justify-center z-50 overflow-hidden"> {/* Added overflow-hidden to parent */}
      {/* Modal Content Wrapper */}
      <div
        className="text-white w-full h-full sm:h-auto sm:max-h-[calc(100vh-2rem)] sm:max-w-xs md:max-w-xl lg:max-w-2xl sm:mx-auto overflow-y-auto p-1 sm:p-0" // Added p-1 for mobile, sm:p-0
        style={{position: 'relative', zIndex: 1}}
      >
        {/* This grid will stack to 1 column on mobile. It should try to fill height. */}
        {/* md:h-auto allows columns to define height on larger screens. */}
        <div className="grid grid-cols-1 md:grid-cols-2 md:gap-4 lg:gap-6 h-full md:h-auto">
          
          {/* SPLIT A (Left Column on md+) */}
          {/* On mobile (flex column), this card can grow. min-h-0 prevents blowout. */}
          <div className="bg-black border-2 border-[var(--red-main-ff0000)] rounded-lg shadow-2xl p-1.5 sm:p-2 flex flex-col space-y-1 sm:space-y-1.5 flex-1 md:flex-initial min-h-0">
            <h2 className="text-sm sm:text-base md:text-md font-bold text-center md:text-left text-red-400 tracking-tight">
              Victory!
            </h2>
            {/* Row 2: Debrief Section - 100% width */}
            <div className="w-full bg-black p-1 sm:p-1.5 rounded-md border border-[var(--red-main-ff0000)]/50">
              <h3 className="text-xs sm:text-sm font-semibold text-red-300 mb-0.5 border-b border-[var(--red-main-ff0000)]/50 pb-0.5">Debrief</h3> {/* Adjusted sm font size */}
              <ProgressBar label="Score" value={score} percentage={100} unit="pts" animationDelay={100} />
              <ProgressBar label="Level" value={playerLevel ?? 'N/A'} percentage={100} animationDelay={300} /> {/* 100 + 200 */}
              <ProgressBar label="Kills" value={enemiesKilled ?? 'N/A'} percentage={100} animationDelay={500} /> {/* 100 + 400 */}
            </div>
            
            {/* Row 3: Report Section and ASCII Art - Stack on small, row on sm+ */}
            <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 sm:space-x-1.5">
              <div className="w-full sm:flex-1 bg-black p-1 sm:p-1.5 rounded-md border border-[var(--red-main-ff0000)]/50">
                <h3 className="text-xs sm:text-sm font-semibold text-red-300 mb-0.5 border-b border-[var(--red-main-ff0000)]/50 pb-0.5">Report</h3> {/* Adjusted sm font size */}
                {/* Score is now in Debrief, keep here or remove? For now, let's assume it's mainly in Debrief. User can decide. */}
                {/* <StatItem label="Score" value={score} unit="pts" /> */}
                <StatItem label="Name" value={blueskyHandle} />
                <StatItem label="Position" value={leaderboardPosition ?? 'N/A'} />
                <StatItem label="Survived for" value={gameDuration ?? 'N/A'} />
                <StatItem label="Badge" value="in development" />
              </div>
              <div className="w-full sm:w-2/5 flex items-center justify-center overflow-hidden p-1 border border-[var(--red-main-ff0000)]/50 rounded-md bg-black">
                {/* ASCII art font size might need further adjustment for very small screens if it causes overflow or looks too large */}
                <pre className="animate-pulse text-red-500 text-[0.3rem] xs:text-[0.35rem] sm:text-[0.4rem] md:text-[0.44rem] lg:text-[0.5rem] leading-none font-mono">
                  {victoryArt}
                </pre>
              </div>
            </div>
            {error && (
              <div className="bg-[var(--red-main-ff0000)]/50 border border-[var(--red-main-ff0000)] text-red-200 text-xs p-1 rounded-md mt-1">
                <p className="font-medium">Error:</p>
                <p>{error}</p>
              </div>
            )}
            <div className="mt-auto">
              <button
                onClick={() => void handlePostToBluesky()}
                disabled={isSubmitting || isPosting}
                className="w-full px-2 py-1 sm:px-2.5 sm:py-1.5 bg-[var(--red-main-ff0000)] text-white font-semibold rounded-md hover:bg-[var(--red-main-ff0000)] focus:bg-[var(--red-main-ff0000)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-1 focus:ring-red-400 focus:ring-offset-1 focus:ring-offset-black text-xs sm:text-sm"
              >
                {isPosting ? 'Posting...' : 'Announce'}
              </button>
            </div>
          </div>

          {/* SPLIT B (Right Column on md+) */}
          {/* On mobile (flex column), this card can grow. min-h-0 prevents blowout. */}
          <div className="bg-black border-2 border-[var(--red-main-ff0000)] rounded-lg shadow-2xl p-1.5 sm:p-2 flex flex-col space-y-1 sm:space-y-1.5 mt-1.5 md:mt-0 flex-1 md:flex-initial min-h-0">
            <h2 className="text-sm sm:text-md font-bold text-center md:text-left text-red-400 tracking-tight">
              Challenge
            </h2>
            <div className="relative md:hidden my-0.5">
              <div className="absolute inset-0 flex items-center" aria-hidden="true">
                <div className="w-full border-t border-[var(--red-main-ff0000)]/40"></div>
              </div>
              <div className="relative flex justify-center">
                <span className="bg-black px-1 text-xs font-medium text-red-400">OR</span>
              </div>
            </div>
            <form onSubmit={(e) => void handleChallengeSubmit(e)} className="space-y-1 sm:space-y-1.5 bg-black p-1 sm:p-1.5 rounded-md border border-[var(--red-main-ff0000)]/50 flex-grow flex flex-col justify-between">
              <div>
                <div>
                  <label htmlFor="challengeHandle1" className="block text-xs font-medium text-red-300 mb-0.5">
                    Player 1 Handle
                  </label>
                  <input
                    id="challengeHandle1"
                    type="text"
                    value={handle1}
                    onChange={(e) => setHandle1(e.target.value.toLowerCase())}
                    placeholder="user.bsky.social"
                    className="w-full px-1.5 py-0.5 sm:px-2 bg-gray-800 border border-[var(--red-main-ff0000)] text-red-200 rounded focus:ring-1 focus:ring-red-500 focus:border-[var(--red-secondary-ff4444)] outline-none placeholder-red-500/60 text-xs"
                    disabled={isSubmitting}
                  />
                </div>
                <div className="mt-0.5 sm:mt-1">
                  <label htmlFor="challengeHandle2" className="block text-xs font-medium text-red-300 mb-0.5">
                    Player 2 (Optional)
                  </label>
                  <input
                    id="challengeHandle2"
                    type="text"
                    value={handle2}
                    onChange={(e) => setHandle2(e.target.value.toLowerCase())}
                    placeholder="user.bsky.social"
                    className="w-full px-1.5 py-0.5 sm:px-2 bg-gray-800 border border-[var(red-main-ff0000)] text-[var(red-main-ff0000)/70 rounded focus:ring-1 focus:ring-red-500 focus:border-[var(red-main-ff0000) outline-none placeholder-red-500/60 text-xs"
                    disabled={isSubmitting}
                  />
                </div>
              </div>
              <div 
                className="relative w-full h-10 sm:h-12 my-1 sm:my-1.5 overflow-hidden"
                style={{
                  maskImage: 'radial-gradient(ellipse 40% 80% at center, black 40%, transparent 80%)',
                  WebkitMaskImage: 'radial-gradient(ellipse 40% 80% at center, black 40%, transparent 80%)',
                }}
              >
                <video
                  src="/audio/challengewirl.mp4"
                  autoPlay
                  loop
                  muted
                  playsInline
                  className="w-full h-full object-cover"
                />
              </div>
              <button
                type="submit"
                disabled={isSubmitting || (!handle1.trim() && !handle2.trim())}
                className="w-full mt-1 sm:mt-1.5 px-2 py-0.5 sm:px-2.5 sm:py-1 border-2 border-[var(red-main-ff0000) text-[var(red-main-ff0000)/70 font-semibold rounded-md hover:bg-red-500 hover:text-black focus:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-1 focus:ring-red-400 focus:ring-offset-1 focus:ring-offset-black text-xs sm:text-sm"
              >
                {isSubmitting && !isPosting ? 'Sending...' : 'Challenge'}
              </button>
            </form>
            <div className="mt-auto pt-1 sm:pt-1.5">
              <button
                onClick={onClose}
                disabled={isSubmitting}
                className="w-full px-2 py-0.5 sm:px-2.5 sm:py-1 border border-gray-600 text-gray-400 font-medium rounded-md hover:bg-gray-700 hover:text-gray-300 focus:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-1 focus:ring-gray-500 focus:ring-offset-1 focus:ring-offset-black text-xs sm:text-sm"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
