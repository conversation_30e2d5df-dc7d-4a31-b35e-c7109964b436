"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useConvexAuth } from "convex/react";

export function SignOutButton() {
  const { isAuthenticated } = useConvexAuth();
  const { signOut } = useAuthActions();

  if (!isAuthenticated) {
    return null;
  }

  return (
    <button
    className=" px-4 py-1 border-2 border-red-500 text-red-500 text-xl font-bold hover:bg-red-500 hover:text-black transition-colors shadow-lg shadow-red-500/20"      onClick={() => void signOut()}
    >
      Sign out
    </button>
  );
}
