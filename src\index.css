@import url('https://fonts.googleapis.com/css2?family=VT323&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  @screen lg {
    html {
      font-size: 200%;
      /* doubles the base font size on lg screens */
    }
  }
}

:root {
  --color-light: #ffffff;
  --color-dark: #171717;
  --black-50: #0000005f;
  --red-main-ff0000: #ff0000;
  --red-secondary-ff4444: #ff4444;
  --yellow: #ffff007d;
}

.accent-text {
  @apply text-slate-600;
}

body {
  font-family: 'VT323', monospace;
  color: var(--color-dark);
  background: var(--color-light);
}

/* only use this to update the style of the auth input fields. use a different class for all other input fields */
.auth-input-field {
  @apply w-full px-4 py-3 rounded-container bg-black border-2 border-red-500 text-red-500 focus:border-red-400 focus:ring-1 focus:ring-red-400 outline-none transition-shadow shadow-sm hover:shadow;
  font-family: 'VT323', monospace;
}

/* only use this to update the style of the auth buttons. use the button class for all other buttons */
.auth-button {
  @apply w-full px-4 py-3 border-2 border-red-500 bg-black text-red-500 font-semibold hover:bg-red-500 hover:text-black transition-colors shadow-sm hover:shadow disabled:opacity-50 disabled:cursor-not-allowed;
  font-family: 'VT323', monospace;
}

/* Custom scrollbar for retro feel */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #000;
}

::-webkit-scrollbar-thumb {
  background: var(--red-main-ff0000);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--red-secondary-ff4444);
}

.menu-opt {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border-bottom: 1px dashed var(--red-main-ff0000);
}

/* Custom range slider styling */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(to right, #d82f2f05, #d82f2f36);
  cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
  background: #330000;
  height: 8px;
  border-radius: 4px;
  border: 1px solid var(--red-main-ff0000);
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: var(--red-main-ff0000);
  height: 16px;
  width: 16px;
  border-radius: 2px;
  border: 1px solid var(--red-main-ff0000);
}

input[type="range"]::-moz-range-track {
  background: #330000;
  height: 8px;
  border-radius: 4px;
  border: 1px solid var(--red-main-ff0000);
}

input[type="range"]::-moz-range-thumb {
  background: var(--red-main-ff0000);
  height: 16px;
  width: 16px;
  border-radius: 2px;
  border: 1px solid var(--red-secondary-ff4444);
  cursor: pointer;
}

/* Custom checkbox styling */
input[type="checkbox"] {
  -webkit-appearance: none;
  appearance: none;
  background: #000;
  border: 2px solid var(--red-main-ff0000);
  width: 16px;
  height: 16px;
  cursor: pointer;
}

input[type="checkbox"]:checked {
  background: var(--red-main-ff0000);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  color: #000;
  font-size: 12px;
  font-weight: bold;
  display: block;
  text-align: center;
  line-height: 12px;
}

.pauseBtn {
  pointer-events: all;
  z-index: 1000000
}