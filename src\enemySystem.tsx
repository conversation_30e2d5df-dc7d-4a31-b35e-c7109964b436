// enemySystem.tsx
// This file contains all enemy-related functionality extracted from SpaceInvadersGameLogic.tsx

import { useCallback } from "react";
import { getEnemySpawnCount } from "./gameLogic/difficultyManager";

// Enemy-related interfaces and types
export interface GameObject {
  x: number;
  y: number;
  width: number;
  height: number;
  vx?: number;
  vy?: number;
}

export interface Enemy extends GameObject {
  type: "basic" | "fast" | "heavy" | "boss";
  health: number;
  maxHealth: number;
  points: number;
  lastShot: number;
  fireRate: number;
  powerLevel?: number;
  attackPattern?: "multiSpread" | "focusedBarrage";
  nextPatternSwitchTime?: number;
  isTelegraphing?: boolean;
  telegraphCompleteTime?: number;
  telegraphColor?: string;
}

export interface SpecialShip extends GameObject {
  type: "barrierBoost" | "autoHeal";
  health: number;
  dropDelayTimer: number;
  hasDroppedCrate: boolean;
  vx: number;
}

export interface Crate extends GameObject {
  type: "barrierBoost" | "autoHeal";
  spawnTime: number;
  vy: number;
}

// Enemy system dependencies interface
export interface EnemySystemDependencies {
  gameStateRef: React.MutableRefObject<{
    enemies: Enemy[];
    lastEnemySpawn: number;
    enemySpawnRate: number;
    isBossActiveRef: boolean;
    lastBossSpawnBlockRef: number;
    globalBossPowerLevelRef: number;
    specialShip: SpecialShip | null;
    crate: Crate | null;
    lastBarrierBoostSpawnLevelBlock: number;
    lastAutoHealSpawnLevelBlock: number;
    gameSpeed: number;
    barrierLine: number;
  }>;
  settings: {
    enemySpawnRate: number;
  };
  level: number;
}

// Constants
const BASE_CANVAS_WIDTH = 507;
const BASE_CANVAS_HEIGHT = 900;

// Enemy system hooks
export const useEnemySystem = (dependencies: EnemySystemDependencies) => {
  const { gameStateRef, settings, level } = dependencies;

  const createEnemy = useCallback(
    (type: Enemy["type"], x: number, y: number) => {
      const currentLevel = level;
      const types = {
        basic: { h: 1, p: 10, fr: 2000, w: 30, hgt: 20, vxMod: 2, vyBase: 1 },
        fast: { h: 1, p: 20, fr: 1500, w: 25, hgt: 15, vxMod: 4, vyBase: 1.2 },
        heavy: {
          h: 3 * 2,
          p: 50,
          fr: 3000,
          w: 40,
          hgt: 30,
          vxMod: 1.5,
          vyBase: 0.8,
        },
        boss: {
          h: 100 + currentLevel,
          p: 200,
          fr: 800,
          w: 60,
          hgt: 40,
          vxMod: 1,
          vyBase: 0.5,
        },
      };
      const c = types[type];
      const enemyData: Enemy = {
        x,
        y,
        width: c.w,
        height: c.hgt,
        vx: (Math.random() - 0.5) * c.vxMod,
        vy: c.vyBase,
        type,
        health: c.h,
        maxHealth: c.h,
        points: c.p,
        lastShot: 0,
        fireRate: c.fr,
      };

      if (type === "boss") {
        enemyData.powerLevel = gameStateRef.current.globalBossPowerLevelRef;
        enemyData.attackPattern = "multiSpread";
        enemyData.nextPatternSwitchTime = 0;
        enemyData.isTelegraphing = false;
        enemyData.telegraphCompleteTime = 0;
        enemyData.telegraphColor = undefined;
      }

      return enemyData;
    },
    [level, gameStateRef]
  );

  const spawnEnemies = useCallback(
    (currentTime: number) => {
      const state = gameStateRef.current;
      const rate = state.enemySpawnRate / settings.enemySpawnRate;

      if (currentTime - state.lastEnemySpawn > rate) {
        let newBossSpawnedThisWave = false;
        if (level >= 20 && !state.isBossActiveRef) {
          const currentBlock = Math.floor((level - 20) / 10);
          if (currentBlock > state.lastBossSpawnBlockRef) {
            const levelInBlock = (level - 20) % 10;
            const baseChance = 0.05;
            const incrementPerLevel = 0.1;
            let spawnProbability =
              baseChance + levelInBlock * incrementPerLevel;

            if (levelInBlock === 9) {
              spawnProbability = 1.0;
            }

            if (Math.random() < spawnProbability) {
              const bossX = BASE_CANVAS_WIDTH / 2 - 30;
              const bossY = -60;
              const newBoss = createEnemy("boss", bossX, bossY);
              if (newBoss.type === "boss") {
                newBoss.nextPatternSwitchTime = currentTime + 8000;
              }
              state.enemies.push(newBoss);
              state.isBossActiveRef = true;
              state.lastBossSpawnBlockRef = currentBlock;
              state.globalBossPowerLevelRef++;
              newBossSpawnedThisWave = true;
            }
          }
        }

        const regularEnemyBaseCount = getEnemySpawnCount(level);
        let actualRegularEnemyCount = regularEnemyBaseCount;

        if (state.isBossActiveRef) {
          actualRegularEnemyCount = Math.floor(regularEnemyBaseCount * 0.3);
        }

        if (!newBossSpawnedThisWave) {
          for (let i = 0; i < actualRegularEnemyCount; i++) {
            const x = Math.random() * (BASE_CANVAS_WIDTH - 60);
            const y = -50 - Math.random() * 200;
            let type: Enemy["type"] = "basic";
            const r = Math.random();

            if (level > 14 && r < 0.3) {
              type = "heavy";
            } else if (level > 8 && r < 0.5) {
              type = "fast";
            }
            state.enemies.push(createEnemy(type, x, y));
          }
        }

        state.lastEnemySpawn = currentTime;
        state.enemySpawnRate = Math.max(600, 2000 - level * 100);
      }
    },
    [level, createEnemy, settings.enemySpawnRate, gameStateRef]
  );

  // const spawnSpecialBonuses = useCallback(
  //   (_currentTime: number) => {
  //     const state = gameStateRef.current;

  //     if (state.specialShip || state.crate) {
  //       return;
  //     }

  //     const currentLevelBlock = Math.floor(level / 5);

  //     // Barrier Boost spawning
  //     if (
  //       currentLevelBlock > state.lastBarrierBoostSpawnLevelBlock &&
  //       level > 4 &&
  //       Math.random() < 0.4
  //     ) {
  //       const specialShip: SpecialShip = {
  //         x: -60,
  //         y: 50 + Math.random() * 100,
  //         width: 50,
  //         height: 25,
  //         vx: 2,
  //         type: "barrierBoost",
  //         health: 3,
  //         dropDelayTimer: 0,
  //         hasDroppedCrate: false,
  //       };
  //       state.specialShip = specialShip;
  //       state.lastBarrierBoostSpawnLevelBlock = currentLevelBlock;
  //       return;
  //     }

  const spawnSpecialBonuses = useCallback(
    (_currentTime: number) => {
      const state = gameStateRef.current;

      if (state.specialShip || state.crate) {
        return;
      }

      const SPECIAL_SHIP_WIDTH = 30;
      const SPECIAL_SHIP_HEIGHT = 20;
      const SPECIAL_SHIP_SPEED = 3;
      let bonusTypeToSpawn: SpecialShip["type"] | null = null;

      const barrierBoostBlockSize = 50;
      const barrierBoostSpawnWindowStart = 45;
      const barrierBoostSpawnWindowEnd = 50;
      const currentBarrierBlock = Math.floor(
        (level - 1) / barrierBoostBlockSize
      );
      const levelWithinBarrierBlock = ((level - 1) % barrierBoostBlockSize) + 1;

      if (
        currentBarrierBlock > state.lastBarrierBoostSpawnLevelBlock &&
        levelWithinBarrierBlock >= barrierBoostSpawnWindowStart &&
        levelWithinBarrierBlock <= barrierBoostSpawnWindowEnd
      ) {
        bonusTypeToSpawn = "barrierBoost";
        state.lastBarrierBoostSpawnLevelBlock = currentBarrierBlock;
      }

      if (!bonusTypeToSpawn) {
        const autoHealBlockSize = 80;
        const autoHealSpawnWindowStart = 75;
        const autoHealSpawnWindowEnd = 85;
        const currentAutoHealBlock = Math.floor(
          (level - 1) / autoHealBlockSize
      );
        const levelWithinAutoHealBlock = ((level - 1) % autoHealBlockSize) + 1;

      if (
          currentAutoHealBlock > state.lastAutoHealSpawnLevelBlock &&
          levelWithinAutoHealBlock >= autoHealSpawnWindowStart &&
          levelWithinAutoHealBlock <= autoHealSpawnWindowEnd
      ) {
          bonusTypeToSpawn = "autoHeal";
          state.lastAutoHealSpawnLevelBlock = currentAutoHealBlock;
        }
      }

      // Auto Heal spawning
  //     if (
  //       currentLevelBlock > state.lastAutoHealSpawnLevelBlock &&
  //       level > 9 &&
  //       Math.random() < 0.3
  //     ) {
  //       const specialShip: SpecialShip = {
  //         x: -60,
  //         y: 50 + Math.random() * 100,
  //         width: 50,
  //         height: 25,
  //         vx: 2,
  //         type: "autoHeal",
  //         health: 3,
  //         dropDelayTimer: 0,
  //         hasDroppedCrate: false,
  //       };
  //       state.specialShip = specialShip;
  //       state.lastAutoHealSpawnLevelBlock = currentLevelBlock;
  //     }
  //   },
  //   [level, gameStateRef]
  // );

     if (bonusTypeToSpawn) {
        const fliesFromLeft = Math.random() < 0.5;
        const newSpecialShip: SpecialShip = {
          x: fliesFromLeft ? -SPECIAL_SHIP_WIDTH : BASE_CANVAS_WIDTH,
          y:
            Math.random() * (BASE_CANVAS_HEIGHT * 0.23) +
            BASE_CANVAS_HEIGHT * 0.1,
          width: SPECIAL_SHIP_WIDTH,
          height: SPECIAL_SHIP_HEIGHT,
          vx: fliesFromLeft ? SPECIAL_SHIP_SPEED : -SPECIAL_SHIP_SPEED,
          type: bonusTypeToSpawn,
          health: 1,
          dropDelayTimer: 0,
          hasDroppedCrate: false,
        };
        state.specialShip = newSpecialShip;
      }
    },
    [level, gameStateRef]
  );

  const updateEnemyMovement = useCallback(
    (currentTime: number) => {
      const state = gameStateRef.current;

          // Barrier line logic
      const BARRIER_LINE_HEIGHT_DIVISOR = 12;
      const barrierPixelY =
        state.barrierLine * (BASE_CANVAS_HEIGHT / BARRIER_LINE_HEIGHT_DIVISOR);

      // Enemy movement and AI

      // Enemy movement and AI
      state.enemies.forEach((e: Enemy) => {
        const intendedY = e.y + (e.vy || 0) * state.gameSpeed;
        if (
          e.vy &&
          e.vy > 0 &&
          e.y + e.height < barrierPixelY &&
          intendedY + e.height >= barrierPixelY
        ) {
          e.y = barrierPixelY - e.height;
          e.vy = 0;
        } else if (e.y + e.height >= barrierPixelY) {
          e.y = barrierPixelY - e.height;
          if (e.vy && e.vy > 0) e.vy = 0;
        } else {
          e.y = intendedY;
        }
        e.x += (e.vx || 0) * state.gameSpeed;
        if (e.x <= 0 || e.x >= BASE_CANVAS_WIDTH - e.width) e.vx = -(e.vx || 0);

        // Boss pattern switching logic
        if (e.type === "boss" && e.nextPatternSwitchTime && currentTime > e.nextPatternSwitchTime) {
          e.attackPattern = e.attackPattern === "multiSpread" ? "focusedBarrage" : "multiSpread";
          e.nextPatternSwitchTime = currentTime + 8000 + Math.random() * 4000;
        }
      });
    },
    [gameStateRef]
  );

  const removeDeadEnemies = useCallback(() => {
    const state = gameStateRef.current;
    const initialEnemyCount = state.enemies.length;

    state.enemies = state.enemies.filter((e: Enemy) => {
      if (e.health <= 0) {
        if (e.type === "boss") {
          state.isBossActiveRef = false;
        }
        return false;
      }
      return true;
    });

    // Return number of enemies killed for scoring
    return initialEnemyCount - state.enemies.length;
  }, [gameStateRef]);

  return {
    createEnemy,
    spawnEnemies,
    spawnSpecialBonuses,
    updateEnemyMovement,
    removeDeadEnemies,
  };
};
