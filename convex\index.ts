import { mutation, query, action } from "./_generated/server";
import { v } from "convex/values";
import { type Id } from "./_generated/dataModel";
import { api } from "./_generated/api";


export const saveScore = mutation({
  args: {
    playerName: v.string(),
    score: v.number(),
    level: v.optional(v.number()),          // Made optional for consistency with schema
    enemiesKilled: v.optional(v.number()),  // Made optional for consistency with schema
    gameDurationMs: v.optional(v.number()), // Added
    isAnonymous: v.optional(v.boolean()), // Added
    blueskyHandle: v.optional(v.string()),
    blueskyDid: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Infer isAnonymous if not explicitly provided by the client
    const isTrulyAnonymous = args.isAnonymous === undefined ? !args.blueskyHandle : args.isAnonymous;

    return await ctx.db.insert("scores", {
      ...args,
      isAnonymous: isTrulyAnonymous, // Ensure isAnonymous is set
      timestamp: Date.now(),
      // challengedHandles: [], // Retaining this default from your existing code
    });
  },
});

export const getHighScores = query({
  args: { 
    paginationOpts: v.optional(v.object({ 
      cursor: v.optional(v.string()), 
      numItems: v.number() 
    })) 
  },
  handler: async (ctx, args) => {
    const opts = args.paginationOpts || { numItems: 100 };
    return await ctx.db
      .query("scores")
      .withIndex("by_score")
      .order("desc")
      .paginate({ 
        numItems: opts.numItems, 
        cursor: opts.cursor === undefined ? null : opts.cursor 
      });
  },
});

// Challenge other players after submitting a score
// Note: validateChallengeHandles action must be called from the client first
// Get a specific score by ID
export const getScore = query({
  args: {
    id: v.id("scores"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Update a score with new data
export const updateScore = mutation({
  args: {
    id: v.id("scores"),
    updates: v.object({
      challengedHandles: v.optional(v.array(v.string())),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id, args.updates);
    return { success: true };
  },
});

export const getScoresByBlueskyHandle = query({
  args: {
    blueskyHandle: v.string(),
    // paginationOpts: v.optional(v.object({ // Future: Add pagination if needed
    //   cursor: v.optional(v.string()),
    //   numItems: v.number()
    // }))
  },
  handler: async (ctx, args) => {
    // const opts = args.paginationOpts || { numItems: 1000 }; // Default to a large number if not paginating
    return await ctx.db
      .query("scores")
      .withIndex("by_blueskyHandle_score")
      .filter(q => q.eq(q.field("blueskyHandle"), args.blueskyHandle))
      .order("desc")
      // .paginate({ // Future: Add pagination if needed
      //   numItems: opts.numItems,
      //   cursor: opts.cursor === undefined ? null : opts.cursor
      // });
      .collect(); // Collect all scores for the handle for now
  },
});

export const getScoreRank = query({
  args: { scoreId: v.id("scores") },
  handler: async (ctx, args) => {
    const myScoreEntry = await ctx.db.get(args.scoreId);
    if (!myScoreEntry) {
      return null; // Or throw an error, or return a specific "not found" rank like -1
    }

    // Fetch all scores. For performance with very large leaderboards,
    // you might optimize this, but for MAX_HIGH_SCORES = 1000, this should be acceptable.
    const allScores = await ctx.db.query("scores").collect();

    // Sort scores:
    // 1. Higher score is better
    // 2. Higher level is better (for ties in score)
    // 3. More enemiesKilled is better (for ties in score and level)
    // 4. Earlier timestamp is better (for ties in score, level, and kills)
    const sortedScores = allScores.sort((a, b) => {
      if (b.score !== a.score) {
        return b.score - a.score;
      }
      // Handle optional fields: treat undefined as 0 or lowest priority
      const levelA = a.level ?? 0;
      const levelB = b.level ?? 0;
      if (levelB !== levelA) {
        return levelB - levelA;
      }

      const killsA = a.enemiesKilled ?? 0;
      const killsB = b.enemiesKilled ?? 0;
      if (killsB !== killsA) {
        return killsB - killsA;
      }

      // For timestamp, earlier is better, so a.timestamp - b.timestamp
      // Treat undefined timestamp as "latest" or less prioritized
      const timestampA = a.timestamp ?? Number.MAX_SAFE_INTEGER;
      const timestampB = b.timestamp ?? Number.MAX_SAFE_INTEGER;
      return timestampA - timestampB;
    });

    const rank = sortedScores.findIndex(score => score._id === args.scoreId);

    return rank !== -1 ? rank + 1 : null; // Return 1-based rank or null if not found
  },
});

// Challenge other players (now an action)
export const challengePlayers = action({
  args: {
    scoreId: v.id("scores"),
    challengedHandles: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    // Run the challenge action
    await ctx.runAction(api.social.postChallenge, {
      scoreId: args.scoreId,
      challengedHandles: args.challengedHandles,
    });
    return { success: true };
  },
});

// Action to submit a score and return the new rank
export const submitScoreAndCheckRank = action({
  args: {
    // Arguments should mirror saveScore
    playerName: v.string(),
    score: v.number(),
    level: v.optional(v.number()),
    enemiesKilled: v.optional(v.number()),
    gameDurationMs: v.optional(v.number()),
    isAnonymous: v.optional(v.boolean()),
    blueskyHandle: v.optional(v.string()),
    blueskyDid: v.optional(v.string()),
  },
  handler: async (
    ctx,
    args
  ): Promise<{ scoreId: Id<"scores">; rank: number | null }> => {
    // 1. Save the score by calling the mutation
    const scoreId = await ctx.runMutation(api.index.saveScore, args);

    // 2. Get the new rank of the score
    const rank = await ctx.runQuery(api.index.getScoreRank, { scoreId });

    // 3. Return the ID and rank to the client
    return { scoreId, rank };
  },
});
