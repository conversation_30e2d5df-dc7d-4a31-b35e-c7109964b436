import React, { useState, useEffect } from 'react';
import { Upgrade, UPGRADES } from '../../gameLogic/upgrades/definitions';

interface UpgradeScreenProps {
  upgrades: Upgrade[];
  onSelect: (upgrade: Upgrade) => void;
  getUpgradeLevel: (upgradeId: Upgrade['id']) => number;
}

export const UpgradeScreen: React.FC<UpgradeScreenProps> = ({ upgrades, onSelect, getUpgradeLevel }) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isShuffling, setIsShuffling] = useState(true);
  const [displayUpgrades, setDisplayUpgrades] = useState<Upgrade[]>([]);
  const [revealed, setRevealed] = useState([false, false, false]);

  useEffect(() => {
    // Initialize with random upgrades
    setDisplayUpgrades([...UPGRADES].sort(() => 0.5 - Math.random()).slice(0, 3));

    const shuffleInterval = setInterval(() => {
      setDisplayUpgrades(currentDisplay => {
        return currentDisplay.map((_, index) => {
          if (revealed[index]) {
            return upgrades[index];
          }
          return UPGRADES[Math.floor(Math.random() * UPGRADES.length)];
        });
      });
    }, 75);

    const revealTimeouts = [
      setTimeout(() => setRevealed(prev => { const next = [...prev]; next[0] = true; return next; }), 800),
      setTimeout(() => setRevealed(prev => { const next = [...prev]; next[1] = true; return next; }), 900),
      setTimeout(() => {
        setRevealed(prev => { const next = [...prev]; next[2] = true; return next; });
        setIsShuffling(false);
        clearInterval(shuffleInterval);
      }, 1000)
    ];

    return () => {
      clearInterval(shuffleInterval);
      revealTimeouts.forEach(clearTimeout);
    };
  }, []); // Run only once on mount

  useEffect(() => {
    if (isShuffling) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'a' || e.key === 'ArrowLeft') {
        setSelectedIndex((prev) => (prev > 0 ? prev - 1 : upgrades.length - 1));
      } else if (e.key === 'd' || e.key === 'ArrowRight') {
        setSelectedIndex((prev) => (prev < upgrades.length - 1 ? prev + 1 : 0));
      } else if (e.key === ' ') {
        onSelect(upgrades[selectedIndex]);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isShuffling, upgrades, selectedIndex, onSelect]);

  const getCardContent = (index: number) => {
    if (revealed[index]) {
      return upgrades[index];
    }
    return displayUpgrades[index];
  };

  return (
    <div className="absolute inset-0 bg-black bg-opacity-75 flex flex-col items-center justify-center z-[120] p-4">
      <h2 className="text-3xl md:text-4xl text-white font-bold mb-8 text-center">CHOOSE YOUR UPGRADE</h2>
      <div className="flex flex-col w-full md:flex-row space-y-4 md:space-y-0 md:space-x-8">
        {[0, 1, 2].map(index => {
          const upgrade = getCardContent(index);
          if (!upgrade) return <div key={index} className="p-6 border-4 rounded-lg border-gray-600 w-68 h-52" />; // Placeholder

          const currentLevel = getUpgradeLevel(upgrade.id);
          const isFinal = revealed[index];

          return (
            <div
              key={`${upgrade.id}-${index}`}
              className={`p-6 border-4 rounded-lg transition-all duration-200 w-72 h-48 flex flex-col justify-between ${
                !isShuffling && selectedIndex === index ? 'border-yellow-400 scale-105 cursor-pointer' : 'border-gray-600'
              } ${!isFinal ? 'opacity-70' : ''}`}
              onClick={() => !isShuffling && onSelect(upgrades[index])}
            >
              <div>
                <h3 className="text-2xl lg:text-xl text-white font-bold">{upgrade.name}</h3>
                <p className="text-gray-300 mt-2 lg:text-xs">{isFinal ? upgrade.description(currentLevel + 1) : '???'}</p>
              </div>
              <p className="text-yellow-400 mt-4">
                Level: {isFinal ? `${currentLevel} / ${upgrade.maxLevel}` : '?'}
              </p>
            </div>
          );
        })}
      </div>
    </div>
  );
};