# Upgrade Details

This document outlines the mechanics of the player upgrade system.

## Existing Upgrades

All current upgrades have a maximum level of **10**.

### 1. Bullet Spread
*   **ID:** `bulletSpread`
*   **Effect:** Provides a percentage chance to fire a 3-way shot.
*   **Scaling:** The chance increases by **1% per level**.
    *   **Level 10 (Max):** 10% chance

### 2. Healthy Bastard
*   **ID:** `healthyBastard`
*   **Effect:** Increases the player's maximum health for the current run.
*   **Scaling:** Adds **10 maximum health points per level**.
    *   **Level 10 (Max):** +100 Max Health

### 3. Vampire Gift
*   **ID:** `vampireGift`
*   **Effect:** Gives a percentage chance to restore 10 HP on enemy hit.
*   **Scaling:** The chance increases by **0.1% per level**.
    *   **Level 10 (Max):** 1.0% chance

---

## New Implemented Upgrades

### 1. Explosive Johnny
*   **ID:** `explosiveJohnny`
*   **Name:** Explosive Johnny
*   **Description:** `+level * 0.5%` chance for bullets to explode, dealing 25% damage in a small radius.
*   **Max Level:** 10

### 2. Sol Invictus
*   **ID:** `solInvictus`
*   **Name:** Sol Invictus
*   **Description:** After taking damage, gain invincibility for `+level * 0.25` seconds.
*   **Max Level:** 2

### 3. Greed is Good
*   **ID:** `greedIsGood`
*   **Name:** Greed is Good
*   **Description:** Increases score from defeated enemies by `level * 2`%.
*   **Max Level:** 10