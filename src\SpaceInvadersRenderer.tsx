import { useCallback, RefObject } from "react";
import { drawPlayerShip } from "./components/gameUI/playerShip";
import { drawAsteroid } from "./components/gameUI/asteroidRenderer";
import { Asteroid } from "./gameLogic/asteroidManager";

// Import types from main file
export interface GameObject {
  x: number;
  y: number;
  width: number;
  height: number;
  vx?: number;
  vy?: number;
}

export interface Player extends GameObject {
  health: number;
  maxHealth: number;
  fireRate: number;
  lastShot: number;
  powerLevel: number;
  activeMultiShotLevel: number;
  multiShotLevelExpireTime: number;
  autoHealCharges: number;
  lastSignificantMoveX: number;
  timeStationaryMs: number;
  isBlinkingRed?: boolean;
  lastPenaltyAppliedAtSecondTier: number;
  invincibilityEndTime: number;
  killStreak: number;
}

interface Enemy extends GameObject {
  type: "basic" | "fast" | "heavy" | "boss";
  health: number;
  maxHealth: number;
  points: number;
  lastShot: number;
  fireRate: number;
  powerLevel?: number;
  attackPattern?: "multiSpread" | "focusedBarrage";
  nextPatternSwitchTime?: number;
  isTelegraphing?: boolean;
  telegraphCompleteTime?: number;
  telegraphColor?: string;
}

export interface Bullet extends GameObject {
  damage: number;
  isPlayerBullet: boolean;
}

interface PowerUp extends GameObject {
  type: "health" | "fireRate" | "multiShot" | "shield";
  duration?: number;
}

export interface Particle extends GameObject {
  life: number;
  maxLife: number;
  color: string;
}

interface SpecialShip extends GameObject {
  type: "barrierBoost" | "autoHeal";
  health: number;
  dropDelayTimer: number;
  hasDroppedCrate: boolean;
  vx: number;
}

interface Crate extends GameObject {
  type: "barrierBoost" | "autoHeal";
  spawnTime: number;
  vy: number;
}

interface FloatingText {
  text: string;
  x: number;
  y: number;
  life: number;
  maxLife: number;
  color: string;
  isStreakBonus?: boolean;
}

interface Explosion {
  x: number;
  y: number;
  radius: number;
  maxRadius: number;
  life: number;
  maxLife: number;
  color: string;
}

interface GameInternalState {
  player: Player;
  enemies: Enemy[];
  bullets: Bullet[];
  powerUps: PowerUp[];
  particles: Particle[];
  floatingTexts: FloatingText[];
  explosions: Explosion[];
  asteroids: Asteroid[];
  keys: Record<string, boolean>;
  lastEnemySpawn: number;
  lastAsteroidSpawn: number;
  lastSoloAsteroidSpawn: number;
  enemySpawnRate: number;
  lastTime: number;
  shakeIntensity: number;
  shakeDecay: number;
  isBossActiveRef: boolean;
  lastBossSpawnBlockRef: number;
  globalBossPowerLevelRef: number;
  barrierLine: number;
  specialShip: SpecialShip | null;
  crate: Crate | null;
  lastBarrierBoostSpawnLevelBlock: number;
  lastAutoHealSpawnLevelBlock: number;
  totalRunTimeMs: number;
  gameSpeed: number;
  timeDilationEndTime: number;
  isMaxStreakActive: boolean;
}

// Renderer dependencies interface
export interface RendererDependencies {
  canvasRef: RefObject<HTMLCanvasElement | null>;
  overlayCanvasRef: RefObject<HTMLCanvasElement | null>;
  gameStateRef: React.MutableRefObject<GameInternalState>;
  backgroundAnimationRef: React.MutableRefObject<number>;
  crtAnimationRef: React.MutableRefObject<number>;
  isMobile: boolean;
  score: number;
  level: number;
  enemiesKilled: number;
  CANVAS_WIDTH: number;
  CANVAS_HEIGHT: number;
  canvasScale: number;
  BASE_CANVAS_WIDTH: number;
  BASE_CANVAS_HEIGHT: number;
}

export const useRenderingHooks = (dependencies: RendererDependencies) => {
  const {
    canvasRef,
    overlayCanvasRef,
    gameStateRef,
    backgroundAnimationRef,
    crtAnimationRef,
    isMobile,
    score,
    level,
    enemiesKilled,
    CANVAS_WIDTH,
    CANVAS_HEIGHT,
    canvasScale,
    BASE_CANVAS_WIDTH,
    BASE_CANVAS_HEIGHT,
  } = dependencies;

  const renderCRTOverlay = useCallback(() => {
    const overlay = overlayCanvasRef.current;
    if (!overlay || isMobile) return;
    const ctx = overlay.getContext("2d");
    if (!ctx) return;
    ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    const time = crtAnimationRef.current;
    const grad = ctx.createRadialGradient(
      CANVAS_WIDTH / 2,
      CANVAS_HEIGHT / 2,
      0,
      CANVAS_WIDTH / 2,
      CANVAS_HEIGHT / 2,
      Math.max(CANVAS_WIDTH, CANVAS_HEIGHT) * 0.8
    );
    grad.addColorStop(0, "rgba(0,0,0,0)");
    grad.addColorStop(0.7, "rgba(0,0,0,0.1)");
    grad.addColorStop(1, "rgba(0,0,0,0.6)");
    ctx.fillStyle = grad;
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    ctx.fillStyle = "rgba(0,0,0,0.15)";
    for (let i = 0; i < CANVAS_HEIGHT; i += 3)
      ctx.fillRect(0, i, CANVAS_WIDTH, 1);
    const scanY = (time * 2) % CANVAS_HEIGHT;
    ctx.fillStyle = "rgba(255,255,255,0.05)";
    ctx.fillRect(0, scanY, CANVAS_WIDTH, 2);
    ctx.globalCompositeOperation = "screen";
    ctx.fillStyle = "rgba(255,0,0,0.02)";
    ctx.fillRect(1, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    ctx.fillStyle = "rgba(0,255,0,0.02)";
    ctx.fillRect(-1, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    ctx.fillStyle = "rgba(0,0,255,0.02)";
    ctx.fillRect(0, 1, CANVAS_WIDTH, CANVAS_HEIGHT);
    ctx.globalCompositeOperation = "source-over";
    if (Math.random() < 0.01) {
      ctx.fillStyle = `rgba(255,255,255,${Math.random() * 0.1})`;
      ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    }
  }, [isMobile, CANVAS_WIDTH, CANVAS_HEIGHT, crtAnimationRef, overlayCanvasRef]);

  const render = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;
    const state = gameStateRef.current;

    ctx.save();
    if (state.shakeIntensity > 0.1)
      ctx.translate(
        (Math.random() - 0.5) * state.shakeIntensity,
        (Math.random() - 0.5) * state.shakeIntensity
      );

    const grad = ctx.createLinearGradient(0, 0, 0, CANVAS_HEIGHT);
    const t = backgroundAnimationRef.current;
    const w1 = Math.sin(t * 0.5) * 0.1 + 0.1;
    const w2 = Math.sin(t * 0.3) * 0.05 + 0.05;
    grad.addColorStop(0, `rgba(${Math.floor(10 + w1 * 20)},0,0,1)`);
    grad.addColorStop(0.3, `rgba(${Math.floor(20 + w2 * 30)},0,0,0.8)`);
    grad.addColorStop(0.7, `rgba(${Math.floor(15 + w1 * 25)},0,0,0.6)`);
    grad.addColorStop(1, `rgba(${Math.floor(5 + w2 * 15)},0,0,1)`);
    ctx.fillStyle = grad;
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    ctx.strokeStyle = "rgba(255,0,0,0.1)";
    ctx.lineWidth = 1 * canvasScale;
    for (let i = 0; i < 10; i++) {
      const yb = (t * 20 + i * 60) % (BASE_CANVAS_HEIGHT + 60);
      const ys = yb * canvasScale;
      ctx.beginPath();
      ctx.moveTo(0, ys);
      ctx.lineTo(CANVAS_WIDTH, ys);
      ctx.stroke();
    }

    const BARRIER_LINE_HEIGHT_DIVISOR = 12;
    const BARRIER_COLOR = "#00FFFF";
    const BARRIER_THICKNESS = 3;
    const barrierYPosition =
      state.barrierLine * (BASE_CANVAS_HEIGHT / BARRIER_LINE_HEIGHT_DIVISOR);

    ctx.fillStyle = BARRIER_COLOR;
    ctx.shadowColor = BARRIER_COLOR;
    ctx.shadowBlur = 5 * canvasScale;
    ctx.fillRect(
      0,
      barrierYPosition * canvasScale - (BARRIER_THICKNESS * canvasScale) / 2,
      CANVAS_WIDTH,
      BARRIER_THICKNESS * canvasScale
    );
    ctx.shadowBlur = 0;

    const p = state.player;
    let determinedPlayerColor = "#ff0000";
    let determinedPlayerInnerColor = "#ff4444";

    if (p.powerLevel === 2) {
      determinedPlayerColor = "#0000ff";
      determinedPlayerInnerColor = "#4444ff";
    } else if (p.powerLevel === 3) {
      determinedPlayerColor = "#00ff00";
      determinedPlayerInnerColor = "#44ff44";
    } else if (p.powerLevel === 4) {
      determinedPlayerColor = "#ffff00";
      determinedPlayerInnerColor = "#ffff44";
    } else if (p.powerLevel >= 5) {
      determinedPlayerColor = "#ff00ff";
      determinedPlayerInnerColor = "#ff44ff";
    }

    const isInvincible = state.lastTime < p.invincibilityEndTime;
    if (isInvincible) {
      if (Math.floor(state.lastTime / 100) % 2 === 0) {
        determinedPlayerColor = "#ffff00";
        determinedPlayerInnerColor = "#ffffcc";
      } else {
        determinedPlayerColor = "#ffffff";
        determinedPlayerInnerColor = "#dddddd";
      }
    }
    drawPlayerShip(
      ctx,
      p,
      determinedPlayerColor,
      determinedPlayerInnerColor,
      canvasScale,
      p.isBlinkingRed,
      state.lastTime
    );

    state.enemies.forEach((e: Enemy) => {
      let c = "#ff3333";
      if (e.type === "fast") c = "#ff6666";
      else if (e.type === "heavy") c = "#cc0000";
      else if (e.type === "boss") c = "#990000";
      ctx.fillStyle = c;
      ctx.shadowColor = c;
      ctx.shadowBlur = 8 * canvasScale;
      ctx.fillRect(
        e.x * canvasScale,
        e.y * canvasScale,
        e.width * canvasScale,
        e.height * canvasScale
      );
      if (e.type === "boss" && e.isTelegraphing && e.telegraphColor) {
        ctx.save();
        ctx.fillStyle = e.telegraphColor;
        const telegraphProgress =
          e.telegraphCompleteTime && e.telegraphCompleteTime > state.lastTime
            ? 1 - (e.telegraphCompleteTime - state.lastTime) / 1000
            : 1;
        const alpha = 0.3 + Math.sin(telegraphProgress * Math.PI * 4) * 0.2;
        const baseColorMatch = e.telegraphColor.match(
          /rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/
        );
        if (baseColorMatch) {
          ctx.fillStyle = `rgba(${baseColorMatch[1]}, ${baseColorMatch[2]}, ${baseColorMatch[3]}, ${Math.max(0.1, Math.min(0.5, alpha))})`;
        } else {
          ctx.fillStyle = e.telegraphColor;
        }
        ctx.fillRect(
          e.x * canvasScale,
          e.y * canvasScale,
          e.width * canvasScale,
          e.height * canvasScale
        );
        ctx.restore();
      }
      if (e.health < e.maxHealth) {
        const hbw = e.width;
        const hbh = 4;
        const hbx = e.x;
        const hby = e.y - 8;
        ctx.fillStyle = "#330000";
        ctx.fillRect(
          hbx * canvasScale,
          hby * canvasScale,
          hbw * canvasScale,
          hbh * canvasScale
        );
        ctx.fillStyle = "var(--red-main-ff0000)";
        ctx.fillRect(
          hbx * canvasScale,
          hby * canvasScale,
          (e.health / e.maxHealth) * hbw * canvasScale,
          hbh * canvasScale
        );
      }
    });

    // state.powerUps.forEach((pu: PowerUp) => {
    //   let c = "#00ff00";
    //   if (pu.type === "fireRate") c = "#ffff00";
    //   else if (pu.type === "multiShot") c = "#ff00ff";
    //   else if (pu.type === "shield") c = "#00ffff";
    //   ctx.fillStyle = c;
    //   ctx.shadowColor = c;
    //   ctx.shadowBlur = 8 * canvasScale;
    //   ctx.fillRect(
    //     pu.x * canvasScale,
    //     pu.y * canvasScale,
    //     pu.width * canvasScale,
    //     pu.height * canvasScale
    //   );
    //   ctx.shadowBlur = 0;
    // });

    if (state.specialShip) {
      let shipColor = "#dddddd";
      if (state.specialShip.type === "barrierBoost") {
        shipColor = "#00FFFF";
      } else if (state.specialShip.type === "autoHeal") {
        shipColor = "#88ff88";
      }
      ctx.fillStyle = shipColor;
      ctx.shadowColor = shipColor;
      ctx.shadowBlur = 8 * canvasScale;
      ctx.fillRect(
        state.specialShip.x * canvasScale,
        state.specialShip.y * canvasScale,
        state.specialShip.width * canvasScale,
        state.specialShip.height * canvasScale
      );
      ctx.shadowBlur = 0;
    }

    state.powerUps.forEach((pu: PowerUp) => {
      ctx.shadowColor = "#ff8888";
      ctx.shadowBlur = 8 * canvasScale;
      ctx.fillStyle = "#ff8888";
      ctx.fillRect(
        pu.x * canvasScale,
        pu.y * canvasScale,
        pu.width * canvasScale,
        pu.height * canvasScale
      );
      ctx.shadowBlur = 0;
      ctx.fillStyle = "#ffffff";
      ctx.font = `${26 * canvasScale}px VT323`;
      ctx.textAlign = "center";
      const i =
        pu.type === "health"
          ? "+"
          : pu.type === "fireRate"
            ? "F"
            : pu.type === "multiShot"
              ? "M"
              : "S";
      ctx.fillText(i, (pu.x + 10) * canvasScale, (pu.y + 14) * canvasScale);
    });

    if (state.crate) {
      let crateColor = "#dddddd";
      let crateSymbol = "?";
      if (state.crate.type === "barrierBoost") {
        crateColor = "#00FFFF";
        crateSymbol = "B";
      } else if (state.crate.type === "autoHeal") {
        crateColor = "#88ff88";
        crateSymbol = "H";
      }
      ctx.fillStyle = crateColor;
      ctx.shadowColor = crateColor;
      ctx.shadowBlur = 8 * canvasScale;
      ctx.fillRect(
        state.crate.x * canvasScale,
        state.crate.y * canvasScale,
        state.crate.width * canvasScale,
        state.crate.height * canvasScale
      );
      ctx.shadowBlur = 0;
      ctx.fillStyle = "#000000";
      ctx.font = `${14 * canvasScale}px VT323`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillText(
        crateSymbol,
        (state.crate.x + state.crate.width / 2) * canvasScale,
        (state.crate.y + state.crate.height / 2) * canvasScale
      );
      ctx.textBaseline = "alphabetic";
    }

    state.asteroids.forEach((asteroid: Asteroid) => {
      drawAsteroid(ctx, asteroid, canvasScale, state.lastTime);
    });

    state.bullets.forEach((b: Bullet) => {
      ctx.shadowColor = b.isPlayerBullet ? "#e2e595" : "#ff4444";
      ctx.shadowBlur = 6 * canvasScale;
      ctx.fillStyle = b.isPlayerBullet ? "#e2e595" : "#ff4444";
      ctx.fillRect(
        b.x * canvasScale,
        b.y * canvasScale,
        b.width * canvasScale,
        b.height * canvasScale
      );
      ctx.shadowBlur = 0;
    });

    state.particles.forEach((pt: Particle) => {
      const a = pt.life / pt.maxLife;
      ctx.fillStyle =
        pt.color + Math.floor(a * 255).toString(16).padStart(2, "0");
      ctx.fillRect(
        pt.x * canvasScale,
        pt.y * canvasScale,
        pt.width * canvasScale,
        pt.height * canvasScale
      );
    });

    state.explosions.forEach((ex: Explosion) => {
      const progress = 1 - ex.life / ex.maxLife;
      const currentRadius = ex.maxRadius * progress;
      const alpha = ex.life / ex.maxLife;
      ctx.save();
      ctx.globalAlpha = alpha;
      ctx.strokeStyle = ex.color;
      ctx.lineWidth = 3 * canvasScale;
      ctx.beginPath();
      ctx.arc(
        ex.x * canvasScale,
        ex.y * canvasScale,
        currentRadius * canvasScale,
        0,
        Math.PI * 2
      );
      ctx.stroke();
      ctx.restore();
    });

    state.floatingTexts.forEach((ft: FloatingText) => {
      if (ft.isStreakBonus) {
        const progress = 1 - ft.life / ft.maxLife;
        const scale = 0.5 + progress * 1.5;
        const opacity = ft.life / ft.maxLife;

        const maxFontSize = CANVAS_WIDTH * 0.2;
        const currentFontSize = maxFontSize * scale;

        ctx.fillStyle = `rgba(255, 255, 0, ${opacity})`;
        ctx.font = `bold ${currentFontSize}px VT323`;
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.fillText(ft.text, ft.x * canvasScale, ft.y * canvasScale);
        ctx.textBaseline = "alphabetic";
      } else {
        const alpha = ft.life / ft.maxLife;
        if (Math.floor(ft.life / 5) % 2 === 0) {
          ctx.fillStyle = `rgba(0, 255, 0, ${alpha})`;
          ctx.font = `bold ${20 * canvasScale}px VT323`;
          ctx.textAlign = "center";
          ctx.fillText(ft.text, ft.x * canvasScale, ft.y * canvasScale);
        }
      }
    });

    ctx.restore();

    if (!isMobile) {
      if (p.activeMultiShotLevel > 0) {
        const DURATION_OF_BOOST = 10000;
        const timeRemaining = p.multiShotLevelExpireTime - state.lastTime;
        const fillRatio = Math.max(
          0,
          Math.min(1, timeRemaining / DURATION_OF_BOOST)
        );
        const barColorForTimer = determinedPlayerColor;
        const timerBarWidthBase = 200;
        const timerBarHeightBase = 10;
        const timerBarXBase = (BASE_CANVAS_WIDTH - timerBarWidthBase) / 2;
        const timerBarYBase = 10;
        ctx.fillStyle = "rgba(100, 100, 100, 0.5)";
        ctx.fillRect(
          timerBarXBase * canvasScale,
          timerBarYBase * canvasScale,
          timerBarWidthBase * canvasScale,
          timerBarHeightBase * canvasScale
        );
        ctx.fillStyle = barColorForTimer;
        ctx.fillRect(
          timerBarXBase * canvasScale,
          timerBarYBase * canvasScale,
          timerBarWidthBase * fillRatio * canvasScale,
          timerBarHeightBase * canvasScale
        );
      }

      const newHbhBase = 20;
      const newHbh = newHbhBase * canvasScale;
      const newHbw = CANVAS_WIDTH * 0.95;
      const newHbx = (CANVAS_WIDTH - newHbw) / 2;

      const healthBarYOffset = 25 * canvasScale;
      const playerBottomY = (p.y + p.height) * canvasScale;
      let newHby = playerBottomY + healthBarYOffset;

      const minMarginFromBottom = 5 * canvasScale;
      if (newHby + newHbh > CANVAS_HEIGHT - minMarginFromBottom) {
        newHby = CANVAS_HEIGHT - newHbh - minMarginFromBottom;
      }
      if (newHby < playerBottomY + 5 * canvasScale) {
        newHby = playerBottomY + 5 * canvasScale;
      }

      ctx.fillStyle = "#330000";
      ctx.fillRect(newHbx, newHby, newHbw, newHbh);

      const healthRatio = Math.max(0, p.health) / p.maxHealth;
      ctx.fillStyle = "#ff0000";
      ctx.fillRect(newHbx, newHby, newHbw * healthRatio, newHbh);

      ctx.fillStyle = "#ffffff";
      const healthBarTextSize = Math.max(12 * canvasScale, 16);
      ctx.font = `bold ${healthBarTextSize}px VT323`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillText(
        `Health: ${Math.max(0, p.health)}`,
        newHbx + newHbw / 2,
        newHby + newHbh / 2
      );

      ctx.textAlign = "left";
      ctx.textBaseline = "alphabetic";

      if (p.isBlinkingRed && state.lastTime) {
        if (Math.floor(state.lastTime / 150) % 2 === 0) {
          const fontSize = CANVAS_WIDTH * 0.2;
          ctx.font = `bold ${fontSize}px VT323`;
          ctx.fillStyle = "rgba(255, 0, 0, 0.3)";
          ctx.textAlign = "center";
          ctx.textBaseline = "middle";
          ctx.fillText("MOVE", CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2);
          ctx.textAlign = "left";
          ctx.textBaseline = "alphabetic";
        }
      }
    }

    if (!isMobile) renderCRTOverlay();
  }, [
    score,
    level,
    enemiesKilled,
    renderCRTOverlay,
    isMobile,
    canvasRef,
    gameStateRef,
    backgroundAnimationRef,
    CANVAS_WIDTH,
    CANVAS_HEIGHT,
    canvasScale,
    BASE_CANVAS_HEIGHT,
  ]);

  return {
    renderCRTOverlay,
    render,
  };
}


