import React, { useRef, useCallback, useState } from 'react';

interface MobileCanvasProps {
  onPlayerMove: (position: number) => void;
}

interface TouchPosition {
  x: number;
  identifier: number | null;
  active: boolean;
}

const MobileCanvas: React.FC<MobileCanvasProps> = ({ onPlayerMove }) => {
  const touchAreaRef = useRef<HTMLDivElement>(null);
  const lastTouchPosition = useRef<number>(0);
  const [touchState, setTouchState] = useState<TouchPosition>({
    x: 0,
    identifier: null,
    active: false
  });
  const [showCircle, setShowCircle] = useState(false);
  const [circlePosition, setCirclePosition] = useState(0);
  
  const handleTouchStart = useCallback((e: React.TouchEvent<HTMLDivElement>) => {
     // Prevent unwanted scrolling
    const touch = e.touches[0];
    if (!touch || !touchAreaRef.current) return;
    
    const controlArea = touchAreaRef.current.getBoundingClientRect();
    const positionPercent = ((touch.clientX - controlArea.left) / controlArea.width) * 100;
    const clampedPercent = Math.max(0, Math.min(100, positionPercent));
    const circleX = (clampedPercent / 100) * controlArea.width;
    
    setTouchState({
      x: touch.clientX,
      identifier: touch.identifier,
      active: true
    });
    setCirclePosition(circleX);
    lastTouchPosition.current = touch.clientX;
    onPlayerMove(clampedPercent);
    setShowCircle(true);
  }, [onPlayerMove]);

  const handleTouchMove = useCallback((e: React.TouchEvent<HTMLDivElement>) => {
    // Prevent unwanted scrolling
    const touch = e.touches[0];
    if (!touch || !touchAreaRef.current) return;
    
    const controlArea = touchAreaRef.current.getBoundingClientRect();
    const positionPercent = ((touch.clientX - controlArea.left) / controlArea.width) * 100;
    const clampedPercent = Math.max(0, Math.min(100, positionPercent));
    const circleX = (clampedPercent / 100) * controlArea.width;
    
    onPlayerMove(clampedPercent);
    setCirclePosition(circleX);
    lastTouchPosition.current = touch.clientX;
  }, [onPlayerMove]); // Remove unnecessary touchState dependency

  const handleTouchEnd = useCallback(() => {
    setTouchState(prev => ({
      ...prev,
      active: false,
      identifier: null
    }));
    
    // Keep the last position when touch ends
    if (!touchAreaRef.current) return;
    
    const controlArea = touchAreaRef.current.getBoundingClientRect();
    const positionPercent = ((lastTouchPosition.current - controlArea.left) / controlArea.width) * 100;
    onPlayerMove(Math.max(0, Math.min(100, positionPercent)));
    setShowCircle(false);
  }, [onPlayerMove]);

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 1000,
        touchAction: 'none',
      }}
    >
      {/* Control area at bottom of screen */}
      <div
        ref={touchAreaRef}
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          width: '100%',
          height: '50%',
          background: 'linear-gradient(to bottom, transparent 0%, rgba(255,0,0,0.05) 50%, rgba(255,0,0,0.1) 100%)',
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onTouchCancel={handleTouchEnd}
      />
      
      {/* Circle indicator */}
      {showCircle && (
        <div
          style={{
            position: 'absolute',
            left: circlePosition,
            bottom: '3%',
            width: '1px',
            height: '80%',
            backgroundColor: 'rgba(255, 0, 0, 0.5)',
            transform: 'translate3d(-50%, 0, 0)',
            transition: 'left cubic-bezier(0.4, 0.0, 0.2, 1)',
            pointerEvents: 'none',
            willChange: 'transform, left'
          }}
        />
      )}
    </div>
  );
};

export default MobileCanvas;

