import React from 'react';
import { Id } from '../../../convex/_generated/dataModel';
import { GameSettings, GameState } from '../../SpaceInvaders'; // Added GameState import

// Props Interfaces
interface PowerUpLegendProps {
  className?: string;
  autoHealCharges: number; // Added autoHealCharges
}

interface ControlsDisplayProps {
  keyAnimationRef: React.MutableRefObject<{ left: number; right: number; space: number }>;
  settings: GameSettings;
  getMovementKeys: () => { left: string; right: string; shoot: string };
  keyMap: () => { left: string; right: string; shoot: string; pause: string; heal: string; };
}

interface ScoreSubmissionScreenProps {
  blueskyHandle: string;
  setBlueskyHandle: (value: string) => void;
  isVerifying: boolean;
  verificationError: string;
  handleSubmitScore: () => Promise<void>;
  showConfirmAnonymous: boolean;
  setShowConfirmAnonymous: (value: boolean) => void;
  handleAnonymousSubmit: () => Promise<void>;
}

interface OptionsScreenProps {
  settings: GameSettings;
  setSettings: React.Dispatch<React.SetStateAction<GameSettings>>;
  setGameState: (state: GameState) => void;
  isMaximized: boolean; 
  onToggleMaximized: () => void; 
}

interface MenuScreenProps {
  startGame: () => void;
  setGameState: (state: 'menu' | 'playing' | 'gameOver' | 'options' | 'submitScore' | 'leaderboard') => void;
}

interface GameOverScreenProps {
  score: number;
  level: number;
  enemiesKilled: number;
  savedScoreId: Id<"scores"> | null;
  startGame: () => void;
  setGameState: (state: 'menu' | 'playing' | 'gameOver' | 'options' | 'submitScore' | 'leaderboard') => void;
}

interface GameScreenProps {
  canvasRef: React.RefObject<HTMLCanvasElement | null>;
  overlayCanvasRef: React.RefObject<HTMLCanvasElement | null>;
  isMobile?: boolean;
  isMaximized?: boolean; 
}

interface MobileStatsDisplayProps {
  score: number;
  level: number;
  enemiesKilled: number;
  playerHealth: number;
}

// Components

export const MobileStatsDisplay: React.FC<MobileStatsDisplayProps> = ({ score, level, enemiesKilled, playerHealth }) => (
  <div className="relative w-full h-full text-red-400 p-2 text-center font-vt323 text-xl bg-black bg-opacity-70">
    <div className="w-full mb-1">SCORE: {score}</div>
    <div className="flex w-full mb-1">
      <div className="w-1/2">LEVEL: {level}</div>
      <div className="w-1/2">KILLS: {enemiesKilled}</div>
    </div>
    <div className="w-full">HEALTH: {playerHealth}</div>
  </div>
);

export const PowerUpLegend: React.FC<PowerUpLegendProps> = ({ className, autoHealCharges }) => {
  const legendItems = [
    { icon: '+', label: 'Health', color: 'bg-red-500 text-md sm:text-lg' },
    { icon: 'F', label: 'Fire Rate', color: 'bg-red-500 text-md sm:text-lg' },
    { icon: 'M', label: 'Multi-Shot', color: 'bg-red-500 text-md sm:text-lg' },
  ];

  return (
    <div className={`flex justify-center items-end gap-0 sm:gap-6 mb-2 text-red-400 text-xs sm:text-sm px-2 bg-black bg-opacity-50 rounded ${className}`}>
      {autoHealCharges > 0 && (
        <div className="flex flex-col items-center gap-1">
          <div className="w-6 h-6 sm:w-7 sm:h-7 bg-green-500 border border-green-300 flex items-center justify-center text-white font-bold text-sm sm:text-md rounded-sm">
            H
          </div>
          <span className="text-green-400">Heal ({autoHealCharges})</span>
        </div>
      )}
      {legendItems.map(item => (
        <div key={item.label} className="flex flex-col items-center gap-1">
          <div className={`w-6 h-6 sm:w-7 sm:h-7 ${item.color || 'bg-red-500'} border border-red-300 flex items-center justify-center text-white font-bold text-sm sm:text-md rounded-sm`}>
            {item.icon}
          </div>
          <span className="text-red-400">{item.label}</span>
        </div>
      ))}
    </div>
  );
};

export const ControlsDisplay: React.FC<ControlsDisplayProps> = ({ keyAnimationRef, settings, getMovementKeys, keyMap }) => {
  const now = Date.now();
  const leftTime = keyAnimationRef.current?.left || 0;
  const rightTime = keyAnimationRef.current?.right || 0;
  const spaceTime = keyAnimationRef.current?.space || 0;

  const leftPressed = now - leftTime < 200;
  const rightPressed = now - rightTime < 200;
  const spacePressed = now - spaceTime < 300;

  const currentKeyMap = keyMap();
  
  const leftKeyText = currentKeyMap.left.length > 1 ? currentKeyMap.left : currentKeyMap.left.toUpperCase();
  const rightKeyText = currentKeyMap.right.length > 1 ? currentKeyMap.right : currentKeyMap.right.toUpperCase();
  const shootKeyText = currentKeyMap.shoot === ' ' ? 'SPACE' : currentKeyMap.shoot.toUpperCase();
  const pauseKeyText = currentKeyMap.pause.toUpperCase();

  return (
    <div className="hidden md:flex flex-col items-center gap-2 p-3 bg-black bg-opacity-60 rounded text-red-400 text-xs shadow-md border border-red-500/30 w-48">
      <div className="font-bold text-sm mb-1">CONTROLS</div>
      <div className="text-xs mb-2">({settings.controlScheme === 'arrows' ? 'Arrow Keys' : 'WASD'})</div>
      <div className="flex flex-col space-y-1.5 w-full">
        <div className={`flex items-center justify-between p-1.5 border rounded-sm ${leftPressed ? 'border-red-500 text-red-300 bg-red-500/20' : 'border-red-500/40'}`}>
          <span className="font-semibold">{leftKeyText}</span>
          <span>Move Left</span>
        </div>
        <div className={`flex items-center justify-between p-1.5 border rounded-sm ${rightPressed ? 'border-red-500 text-red-300 bg-red-500/20' : 'border-red-500/40'}`}>
          <span className="font-semibold">{rightKeyText}</span>
          <span>Move Right</span>
        </div>
        <div className={`flex items-center justify-between p-1.5 border rounded-sm ${spacePressed ? 'border-red-500 text-red-300 bg-red-500/20' : 'border-red-500/40'}`}>
          <span className="font-semibold">{shootKeyText}</span>
          <span>Shoot</span>
        </div>
        <div className="flex items-center justify-between p-1.5 border border-red-500/40 rounded-sm">
          <span className="font-semibold">{pauseKeyText}</span>
          <span>Pause</span>
        </div>
      </div>
    </div>
  );
};

export const ScoreSubmissionScreen: React.FC<ScoreSubmissionScreenProps> = ({
  blueskyHandle,
  setBlueskyHandle,
  isVerifying,
  verificationError,
  handleSubmitScore,
  showConfirmAnonymous,
  setShowConfirmAnonymous,
  handleAnonymousSubmit
}) => {
  return (
    <div className="flex items-center justify-center min-h-screen bg-black text-red-500 p-4">
      <div className="relative w-full max-w-md p-6 border-2 border-red-500 bg-black shadow-lg shadow-red-500/50">
        <h1 className="text-3xl mb-6 text-center">Submit Your Score</h1>
        
        {!showConfirmAnonymous ? (
          <>
            <div className="mb-6">
              <label className="block mb-2">Bluesky Handle (e.g. @username.bsky.social)</label>
              <input
                type="text"
                value={blueskyHandle}
                onChange={(e) => setBlueskyHandle(e.target.value)}
                className="auth-input-field lowercase"
                placeholder="username.bsky.social"
              />
              {verificationError && (
                <p className="text-red-400 mt-2">{verificationError}</p>
              )}
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                className="auth-button flex-1"
                onClick={() => {
                  void handleSubmitScore();
                }}
                disabled={isVerifying || !blueskyHandle.trim()}
              >
                {isVerifying ? 'Verifying...' : 'Submit with Bluesky'}
              </button>
            </div>
            
            <div className="mt-4 text-center">
              <button
                className="text-red-400 underline hover:text-red-300"
                onClick={() => setShowConfirmAnonymous(true)}
              >
                Submit Anonymously
              </button>
            </div>
          </>
        ) : (
          <div className="text-center">
            <p className="mb-6">Are you sure you want to submit your score anonymously?</p>
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                className="auth-button flex-1"
                onClick={() => {
                  void handleAnonymousSubmit();
                }}
              >
                Yes, Submit Anonymously
              </button>
              <button
                className="auth-button flex-1 bg-gray-600 hover:bg-gray-500"
                onClick={() => setShowConfirmAnonymous(false)}
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export const OptionsScreen: React.FC<OptionsScreenProps> = ({
  settings,
  setSettings,
  setGameState,
  isMaximized: _isMaximized, // Unused - maximize functionality moved to game screen only
  onToggleMaximized: _onToggleMaximized, // Unused - maximize functionality moved to game screen only
}) => {
  return (
    <div className="flex items-center justify-center text-red-500 p-4 overflow-y-auto">
      <div className="relative w-full max-w-md p-6 border-2 border-red-500 bg-black shadow-lg shadow-red-500/50">
        <h1 className="text-3xl mb-6 text-center">Options</h1>
        
        <div className="mb-0 menu-opt">
          <label className="block mb-1 text-sm">Enemy Spawn Rate: {settings.enemySpawnRate.toFixed(1)}x</label>
          <input type="range" min="0.5" max="2" step="0.1" value={settings.enemySpawnRate} onChange={(e) => setSettings(prev => ({ ...prev, enemySpawnRate: parseFloat(e.target.value) }))} className="w-full accent-red-500 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer" />
        </div>
        
        <div className="mb-0 menu-opt">
          <label className="block mb-1 text-sm">Player Fire Rate: {settings.playerFireRate.toFixed(1)}x</label>
          <input type="range" min="0.5" max="2" step="0.1" value={settings.playerFireRate} onChange={(e) => setSettings(prev => ({ ...prev, playerFireRate: parseFloat(e.target.value) }))} className="w-full accent-red-500 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer" />
        </div>

        <div className="mb-0 menu-opt">
          <label className="block mb-1 text-sm">Enemy Bullet Speed: {settings.enemyBulletSpeed.toFixed(1)}x</label>
          <input type="range" min="0.5" max="2.0" step="0.1" value={settings.enemyBulletSpeed} onChange={(e) => setSettings(prev => ({ ...prev, enemyBulletSpeed: parseFloat(e.target.value) }))} className="w-full accent-red-500 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer" />
        </div>

        <div className="mb-0 menu-opt">
          <label className="block mb-1 text-sm">Player Bullet Speed: {settings.playerBulletSpeed.toFixed(1)}x</label>
          <input type="range" min="0.5" max="2.0" step="0.1" value={settings.playerBulletSpeed} onChange={(e) => setSettings(prev => ({ ...prev, playerBulletSpeed: parseFloat(e.target.value) }))} className="w-full accent-red-500 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer" />
        </div>
        
        <div className="mb-0 menu-opt">
          <label className="block mb-1 text-sm">Music Volume: {Math.round(settings.musicVolume * 100)}%</label>
          <input type="range" min="0" max="1" step="0.05" value={settings.musicVolume} onChange={(e) => setSettings(prev => ({ ...prev, musicVolume: parseFloat(e.target.value) }))} className="w-full accent-red-500 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer" />
        </div>
        
        <div className="mb-0 menu-opt">
          <label className="block mb-1 text-sm">SFX Volume: {Math.round(settings.sfxVolume * 100)}%</label>
          <input type="range" min="0" max="1" step="0.05" value={settings.sfxVolume} onChange={(e) => setSettings(prev => ({ ...prev, sfxVolume: parseFloat(e.target.value) }))} className="w-full accent-red-500 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer" />
        </div>
        
        <div className="mb-0 flex items-center">
          <input type="checkbox" id="musicEnabled" checked={settings.musicEnabled} onChange={(e) => setSettings(prev => ({ ...prev, musicEnabled: e.target.checked }))} className="w-4 h-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500 accent-red-500" />
          <label htmlFor="musicEnabled" className="ml-2 text-sm">Enable Music</label>
        </div>
        
        <div className="mb-0 flex items-center">
          <input type="checkbox" id="sfxEnabled" checked={settings.sfxEnabled} onChange={(e) => setSettings(prev => ({ ...prev, sfxEnabled: e.target.checked }))} className="w-4 h-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500 accent-red-500" />
          <label htmlFor="sfxEnabled" className="ml-2 text-sm">Enable Sound Effects</label>
        </div>
        
        <div className="mb-0">
          <label className="block mb-2 text-sm">Control Scheme</label>
          <div className="flex gap-4">
            <button className={`auth-button flex-1 ${settings.controlScheme === 'arrows' ? 'bg-red-600' : 'bg-red-800 hover:bg-red-700'}`} onClick={() => setSettings(prev => ({ ...prev, controlScheme: 'arrows' }))}>
              Arrow Keys
            </button>
            <button className={`auth-button flex-1 ${settings.controlScheme === 'wasd' ? 'bg-red-600' : 'bg-red-800 hover:bg-red-700'}`} onClick={() => setSettings(prev => ({ ...prev, controlScheme: 'wasd' }))}>
              WASD
            </button>
          </div>
        </div>
        
        {/* Maximize button hidden - functionality moved to game screen only */}
        
        <button className="auth-button w-full mt-6" onClick={() => setGameState('menu')}>
          Back to Menu
        </button>
      </div>
    </div>
  );
};

export const MenuScreen: React.FC<MenuScreenProps> = ({ startGame, setGameState }) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen w-[100vw] bg-black-50 text-red-500">
      <h1 className="text-5xl sm:text-7xl mb-8 font-vt323 animate-pulse">ARCADE INVADERS
         <span className="block text-white text-center text-4xl">
          BETA
        </span>
      </h1>
      <div className="w-full max-w-xs space-y-4">
        <button className="auth-button text-xl w-full" onClick={startGame}>
          START GAME
        </button>
        <button className="auth-button text-xl w-full" onClick={() => setGameState('options')}>
          OPTIONS
        </button>
        <button className="auth-button text-xl w-full" onClick={() => setGameState('leaderboard')}>
          LEADERBOARDS
        </button>
      </div>
    </div>
  );
};

export const GameOverScreen: React.FC<GameOverScreenProps> = ({
  score,
  level,
  enemiesKilled,
  savedScoreId,
  startGame,
  setGameState
}) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-black text-red-500 px-4">
      <h1 className="text-5xl sm:text-7xl mb-8 font-vt323">GAME OVER</h1>
      
      <div className="w-full max-w-md border-2 border-red-500 p-6 mb-8 bg-black bg-opacity-50">
        <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-lg">
          <span>Score:</span>
          <span className="text-right font-bold">{score}</span>
          
          <span>Level:</span>
          <span className="text-right font-bold">{level}</span>
          
          <span>Enemies Destroyed:</span>
          <span className="text-right font-bold">{enemiesKilled}</span>
        </div>
      </div>
      
      <div className="w-full max-w-xs space-y-4">
        <button className="auth-button text-xl w-full" onClick={startGame}>
          PLAY AGAIN
        </button>
        
        {!savedScoreId && (
          <button className="auth-button text-xl w-full" onClick={() => setGameState('submitScore')}>
            SUBMIT SCORE
          </button>
        )}
        
        <button className="auth-button text-xl w-full" onClick={() => setGameState('menu')}>
          MAIN MENU
        </button>
      </div>
    </div>
  );
};

export const GameScreen: React.FC<GameScreenProps> = ({ canvasRef, overlayCanvasRef, isMobile, isMaximized }) => {
  if (isMaximized) {
    return (
      <div 

        className="fixed align flex justify-center inset-0 z-[100] bg-black border-2 border-red-700 shadow-2xl shadow-red-500/50" // Using inset-0 for true fullscreen
      >
        <canvas ref={canvasRef} className="block w-screen h-screen"></canvas>
        <canvas ref={overlayCanvasRef} className={`absolute inset-0 !w-screen h-screen ${!isMobile ? 'pointer-events-none' : ''}`}></canvas>
      </div>
    );
  } else {
    return (
      <div 
        id="fullscreenMode" // Restored ID (though might be better to have distinct IDs or none for standard view)
        className="relative border-2 border-red-700 shadow-2xl shadow-red-500/50"
        style={{ width: 'var(--canvas-width)', height: 'var(--canvas-height)' }}
      >
        <canvas ref={canvasRef} className="block"></canvas>
        <canvas ref={overlayCanvasRef} className={`absolute top-0 left-0 inset-0 w-screen !h-screen ${!isMobile ? 'pointer-events-none' : ''}`}></canvas>
      </div>
    );
  }
};
