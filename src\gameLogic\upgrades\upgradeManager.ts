import { UPGRADES, Upgrade } from './definitions';

export type PlayerUpgrade = {
  id: Upgrade['id'];
  level: number;
};

export class UpgradeManager {
  private availableUpgrades: Upgrade[];
  public playerUpgrades: PlayerUpgrade[] = [];

  constructor() {
    this.availableUpgrades = [...UPGRADES];
  }

  getUpgradeOptions(): Upgrade[] {
    const unmaxedUpgrades = this.availableUpgrades.filter(upgrade => {
      const playerUpgrade = this.playerUpgrades.find(u => u.id === upgrade.id);
      return !playerUpgrade || playerUpgrade.level < upgrade.maxLevel;
    });

    const options: Upgrade[] = [];
    const shuffled = [...unmaxedUpgrades].sort(() => 0.5 - Math.random());

    while (options.length < 3 && shuffled.length > 0) {
      const nextUpgrade = shuffled.pop();
      if (nextUpgrade) {
        options.push(nextUpgrade);
      }
    }
    return options;
  }

  levelUp(upgradeId: Upgrade['id']) {
    const existingUpgrade = this.playerUpgrades.find((u) => u.id === upgradeId);

    if (existingUpgrade) {
      const upgradeDef = this.availableUpgrades.find(u => u.id === upgradeId);
      if (upgradeDef && existingUpgrade.level < upgradeDef.maxLevel) {
        existingUpgrade.level++;
      }
    } else {
      this.playerUpgrades.push({ id: upgradeId, level: 1 });
    }
  }

  getUpgradeLevel(upgradeId: Upgrade['id']): number {
    return this.playerUpgrades.find((u) => u.id === upgradeId)?.level ?? 0;
  }
}