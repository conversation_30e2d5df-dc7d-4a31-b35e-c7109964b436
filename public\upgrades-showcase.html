<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Upgrades Showcase - Retro Space Invaders</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=VT323&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    :root {
      --color-light: #ffffff;
      --color-dark: #171717;
      --black-50: #0000005f;
      --red-main-ff0000: #ff0000;
      --red-secondary-ff4444: #ff4444;
    }


    body {
      font-family: 'VT323', monospace;
      background-color: #000;
      color: #fff;
    }

    .upgrade-card,
    .powerup-card,
    .killstreak-card {
      border: 4px solid #4a5568;
      border-radius: 0.5rem;
      padding: 1.5rem;
      transition: all 0.2s ease-in-out;
      margin-bottom: 2rem;

    }

    .upgrade-card:hover,
    .powerup-card:hover {
      border-color: #fbbF24;
      transform: scale(1.05);
    }

    .upgrade-card h3 {
      font-size: 2rem;
    }

    .upgrade-card p {
      font-size: 1.5rem;
    }

    .killstreak-card {
      width: 100%;
    }

    .powerup-icon {
      font-size: 2.5rem;
      line-height: 1;
      color: #ff8888;
      text-shadow: 0 0 8px #ff8888;
      justify-content: space-around;
      align-items: space-around;
      display: flex;
      width: 100%;
    }
  </style>
</head>

<body
  class="bg-black m-auto max-w-[1200px] flex flex-col justify-center items-center justify-center content-center align-center min-h-screen p-4">

  <!-- Power-ups Section -->

  <div
    class="p-8 flex border-2 border-[var(--red-main-ff0000)] rounded-md flex-wrap w-full items-around justify-between  gap-8 mb-16">
    <h1 class="text-5xl   text-center text-white font-bold mb-8">POWER-UPS
    </h1>
    <div class="powerup-card text-center">
      <div class="powerup-icon">+</div>
      <h3 class="text-xl font-bold mt-2">Health</h3>
    </div>
    <div class="powerup-card text-center">
      <div class="powerup-icon">F</div>
      <h3 class="text-xl font-bold mt-2">Fire Rate</h3>
    </div>
    <div class="powerup-card text-center">
      <div class="powerup-icon">M</div>
      <h3 class="text-xl font-bold mt-2">Multi-Shot</h3>
    </div>
    <div class="powerup-card text-center">
      <div class="powerup-icon">S</div>
      <h3 class="text-xl font-bold mt-2">Shield</h3>
    </div>
  </div>

  <!-- Upgrades Section -->
  <div class="grid p-8 border-2 border-[var(--red-main-ff0000)] rounded-md grid-cols-1 md:grid-cols-3 gap-8">
    <!-- Row 1 -->
    <h1 class="text-5xl block text-white font-bold mb-12">UPGRADES</h1>

    <div class="upgrade-card">
      <div>
        <h3 class="text-4xl text-white font-bold">Bullet Spread</h3>
        <p class="text-gray-300 !text-lg mt-2">+1% chance for a x3 bullet spread.</p>
      </div>
      <p class="text-yellow-400 mt-4">Level: 1 / 10</p>
    </div>
    <div class="upgrade-card">
      <div>
        <h3 class="text-4xl text-white font-bold">Healthy Bastard</h3>
        <p class="text-gray-300 mt-2">+10 to max health for this run.</p>
      </div>
      <p class="text-yellow-400 mt-4">Level: 1 / 10</p>
    </div>
    <div class="upgrade-card">
      <div>
        <h3 class="text-4xl text-white font-bold">Vampire Gift</h3>
        <p class="text-gray-300 mt-2">+0.1% chance on hit to heal 10 HP.</p>
      </div>
      <p class="text-yellow-400 mt-4">Level: 1 / 10</p>
    </div>
    <!-- Row 2 -->
    <div class="upgrade-card">
      <div>
        <h3 class="text-4xl text-white font-bold">Explosive Johnny</h3>
        <p class="text-gray-300 mt-2">+0.5% chance for bullets to explode, dealing 25% damage in a small radius.</p>
      </div>
      <p class="text-yellow-400 mt-4">Level: 1 / 10</p>
    </div>
    <div class="upgrade-card">
      <div>
        <h3 class="text-4xl text-white font-bold">Sol Invictus</h3>
        <p class="text-gray-300 mt-2">After taking damage, gain invincibility for +0.25s.</p>
      </div>
      <p class="text-yellow-400 mt-4">Level: 1 / 2</p>
    </div>
    <div class="upgrade-card">
      <div>
        <h3 class="text-4xl text-white font-bold">Greed is Good</h3>
        <p class="text-gray-300 mt-2">Increases score from defeated enemies by 2%.</p>
      </div>
      <p class="text-yellow-400 mt-4">Level: 1 / 10</p>
    </div>
  </div>

  <!-- Kill Streak Section -->
  <div class="p-8 border-2 border-[var(--red-main-ff0000)] rounded-md mt-16 w-full ">
    <h1 class="text-5xl text-white font-bold mb-8 text-center">KILL STREAK BONUS</h1>
    <div class="killstreak-card bg-gray-900 p-8">
      <p class="text-2xl text-center mb-6">Reward skillful play by achieving consecutive kills without taking damage.
      </p>
      <div class="flex justify-around text-center">
        <div>
          <h3 class="text-4xl text-yellow-400 font-bold">10 Kills</h3>
          <p class="text-gray-300 mt-2">+10 Score Bonus</p>
        </div>
        <div>
          <h3 class="text-4xl text-yellow-400 font-bold">25 Kills</h3>
          <p class="text-gray-300 mt-2">+25 Score Bonus</p>
        </div>
        <div>
          <h3 class="text-4xl text-yellow-400 font-bold">50 Kills</h3>
          <p class="text-gray-300 mt-2">+50 Score Bonus & Flashing Border</p>
        </div>
      </div>
      <p class="text-center text-red-500 mt-8 text-2xl">The streak resets to zero if you take damage!</p>
    </div>
  </div>

</body>

</html>