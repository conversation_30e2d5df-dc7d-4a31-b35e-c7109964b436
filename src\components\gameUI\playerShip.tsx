import { Player } from '../../SpaceInvaders'; 

export function drawPlayerShip(
  ctx: CanvasRenderingContext2D,
  player: Player,
  determinedPlayerColor: string,
  determinedPlayerInnerColor: string,
  canvasScale: number,
  isBlinkingRed?: boolean, // New parameter for blinking state
  currentTime?: number    // New parameter for blink timing
): void {
  ctx.save();

  const x = player.x * canvasScale;
  const y = player.y * canvasScale;
  const width = player.width * canvasScale;
  const height = player.height * canvasScale;

  let currentShipColor = 'rgba(0, 0, 0, 0)'; // Default determinedPlayerColor
  let currentShipShadowColor = determinedPlayerColor;
  let cockpitColor = 'rgba(0, 0, 0, 0)';
  let detailStrokeColor = determinedPlayerInnerColor; // For wing lines etc.

  if (isBlinkingRed && currentTime && Math.floor(currentTime / 200) % 2 === 0) { // Blink rate
    currentShipColor = 'rgba(255, 0, 0, 0.9)'; // Blinking red color
    currentShipShadowColor = 'rgba(255, 0, 0, 0.9)';
    cockpitColor = 'rgba(255, 100, 100, 0.9)'; // Lighter red for cockpit during blink
    detailStrokeColor = 'rgba(255, 100, 100, 0.9)';
  }

  // Base ship shape (triangle)
  ctx.beginPath();
  ctx.moveTo(x + width / 2, y); // Top point
  ctx.lineTo(x + width, y + height); // Bottom right
  ctx.lineTo(x, y + height); // Bottom left
  ctx.closePath();
  ctx.fillStyle = currentShipColor;
  ctx.shadowColor = currentShipShadowColor;
  ctx.shadowBlur = 10 * canvasScale;
  ctx.fill();
  ctx.shadowBlur = 0;

  // Inner cockpit/detail (smaller triangle)
  ctx.beginPath();
  ctx.moveTo(x + width / 2, y + height * 0.2); 
  ctx.lineTo(x + width * 0.8, y + height * 0.85); 
  ctx.lineTo(x + width * 0.2, y + height * 0.85); 
  ctx.closePath();
  ctx.strokeStyle = detailStrokeColor; // Use the detail color for the outline
  ctx.lineWidth = 1.5 * canvasScale;   // Give it some thickness, consistent with other details
  ctx.stroke();

  // Power Level Morphing
  if (player.powerLevel >= 2) {
    // Internal wing lines
    ctx.strokeStyle = detailStrokeColor; // Use potentially blinking color
    ctx.lineWidth = 1.5 * canvasScale;
    ctx.beginPath();
    // Left wing line
    ctx.moveTo(x + width / 2, y + height * 0.4);
    ctx.lineTo(x + width * 0.1, y + height * 0.7);
    // Right wing line
    ctx.moveTo(x + width / 2, y + height * 0.4);
    ctx.lineTo(x + width * 0.9, y + height * 0.7);
    ctx.stroke();
  }

  if (player.powerLevel >= 4) {
    // Swept pattern (additional lines or small filled areas)
    // Use main ship color for these accents, which will blink if ship is blinking
    ctx.fillStyle = currentShipColor; 
    // Small triangles near wing base
    ctx.beginPath();
    ctx.moveTo(x + width * 0.4, y + height * 0.6);
    ctx.lineTo(x + width * 0.25, y + height * 0.8);
    ctx.lineTo(x + width * 0.45, y + height * 0.8);
    ctx.closePath();
    ctx.fill();

    ctx.beginPath();
    ctx.moveTo(x + width * 0.6, y + height * 0.6);
    ctx.lineTo(x + width * 0.75, y + height * 0.8);
    ctx.lineTo(x + width * 0.55, y + height * 0.8);
    ctx.closePath();
    ctx.fill();

    // Internal "cannons" - small rectangles near the front sides of the main body
    const cannonWidth = width * 0.1;
    const cannonHeight = height * 0.4; 
    ctx.fillStyle = cockpitColor; // Cannons use cockpit color, will blink
    // Left cannon
    ctx.fillRect(x + width * 0.15, y + height * 0.3, cannonWidth, cannonHeight);
    // Right cannon
    ctx.fillRect(x + width * 0.75, y + height * 0.3, cannonWidth, cannonHeight);
  }

  if (player.powerLevel >= 5) {
    // Enhanced details - e.g., slightly larger internal cannons or additional lines
    // Center line detail
    ctx.beginPath();
    ctx.moveTo(x + width / 2, y + height * 0.1);
    ctx.lineTo(x + width / 2, y + height * 0.5);
    ctx.lineWidth = 2 * canvasScale;
    ctx.strokeStyle = detailStrokeColor; // Use potentially blinking color
    ctx.stroke();

    // Slightly "thicker" cannon representation by adding an outline or highlight
    // Outline uses main ship color, will blink
    ctx.strokeStyle = currentShipColor; 
    ctx.lineWidth = 1 * canvasScale;
    const cannonWidthForOutline = width * 0.1; 
    const cannonHeightForOutline = height * 0.4; 
    // Left cannon outline
    ctx.strokeRect(x + width * 0.15 - 1*canvasScale, y + height * 0.3 - 1*canvasScale, cannonWidthForOutline + 2*canvasScale, cannonHeightForOutline + 2*canvasScale);
    // Right cannon outline
    ctx.strokeRect(x + width * 0.75 - 1*canvasScale, y + height * 0.3 - 1*canvasScale, cannonWidthForOutline + 2*canvasScale, cannonHeightForOutline + 2*canvasScale);
  }

  // Fire Rate Morphing (Engine Flare) - Should probably not blink red with damage
  if (player.fireRate <= 200) {
    const flareBaseSize = height * 0.3;
    let flareHeight = flareBaseSize;
    let flareColorAlpha = 0.6;

    if (player.fireRate <= 120) {
      flareHeight = flareBaseSize * 1.5; // Larger flare
      flareColorAlpha = 0.8; // More intense
    }
    
    flareHeight *= (1 + (player.powerLevel -1) * 0.1);

    ctx.fillStyle = `rgba(255, 165, 0, ${flareColorAlpha})`; 
    ctx.beginPath();
    ctx.moveTo(x + width / 2, y + height); 
    ctx.lineTo(x + width * 0.4, y + height + flareHeight * 0.8);
    ctx.lineTo(x + width / 2, y + height + flareHeight); 
    ctx.lineTo(x + width * 0.6, y + height + flareHeight * 0.8);
    ctx.closePath();
    ctx.fill();
  }

  ctx.restore();
}
