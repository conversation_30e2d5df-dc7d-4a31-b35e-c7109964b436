import { Asteroid } from '../../gameLogic/asteroidManager';

/**
 * Draws a single asteroid on the canvas.
 * @param ctx The 2D rendering context of the canvas.
 * @param asteroid The asteroid object to draw.
 * @param canvasScale The scaling factor for the canvas dimensions.
 */
export function drawAsteroid(ctx: CanvasRenderingContext2D, asteroid: Asteroid, canvasScale: number, currentTime: number): void {
  ctx.save();

  let opacity = 1.0;
  if (asteroid.isFading) {
    const fadeDuration = 200;
    const elapsed = currentTime - asteroid.fadeStartTime;
    opacity = Math.max(0, 1 - elapsed / fadeDuration);
  }

  const scaledX = asteroid.x * canvasScale;
  const scaledY = asteroid.y * canvasScale;

  ctx.translate(scaledX + (asteroid.width / 2) * canvasScale, scaledY + (asteroid.height / 2) * canvasScale);

  // Draw background glow
  ctx.beginPath();
  ctx.moveTo(asteroid.shape[0].x * canvasScale, asteroid.shape[0].y * canvasScale);
  for (let i = 1; i < asteroid.shape.length; i++) {
    ctx.lineTo(asteroid.shape[i].x * canvasScale, asteroid.shape[i].y * canvasScale);
  }
  ctx.closePath();
  ctx.fillStyle = `rgba(139, 0, 0, ${0.2 * opacity})`; // Dark Red at 20% opacity
  ctx.fill();

  // Draw vexel lines
  ctx.strokeStyle = `rgba(255, 0, 0, ${opacity})`; // Neon Red
  ctx.lineWidth = 2 * canvasScale;
  ctx.shadowColor = `rgba(255, 0, 0, ${opacity})`;
  ctx.shadowBlur = 10 * canvasScale;
  ctx.stroke();

  ctx.restore();

  // Health bar (drawn without translation)
  if (asteroid.health < asteroid.maxHealth && !asteroid.isFading) {
    const healthBarWidth = asteroid.width * 0.8 * canvasScale;
    const healthBarHeight = 5 * canvasScale;
    const healthBarX = (asteroid.x + asteroid.width * 0.1) * canvasScale;
    const healthBarY = (asteroid.y + asteroid.height + 10) * canvasScale;
    
    const healthRatio = asteroid.health / asteroid.maxHealth;

    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight);

    ctx.fillStyle = healthRatio > 0.5 ? '#4CAF50' : healthRatio > 0.2 ? '#FFC107' : '#F44336';
    ctx.fillRect(healthBarX, healthBarY, healthBarWidth * healthRatio, healthBarHeight);
  }
}