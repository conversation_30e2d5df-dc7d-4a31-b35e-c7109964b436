import { mutation, internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";

// Migration to add timestamps to old scores (internal function)
export const addTimestampsToScores = internalMutation({
  args: {},
  handler: async (ctx) => {
    // Get all scores without timestamps
    const scores = await ctx.db
      .query("scores")
      .withIndex("by_score")
      .filter((q) => q.eq(q.field("timestamp"), undefined))
      .collect();

    // Update each score with a timestamp based on _creationTime
    for (const score of scores) {
      await ctx.db.patch(score._id, {
        timestamp: score._creationTime,
      });
    }
  },
});

// Public function to run migration
export const runTimestampMigration = mutation({
  args: {},
  handler: async (ctx) => {
    await ctx.runMutation(internal.migrations.addTimestampsToScores);
    return { success: true, message: "Migration completed successfully" };
  },
});
