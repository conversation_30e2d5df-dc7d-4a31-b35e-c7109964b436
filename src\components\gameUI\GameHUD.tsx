import React, { useEffect, useRef, useState } from 'react';

interface GameHUDProps {
  score: number;
  level: number;
  lastLevel: number; // To detect level up
  enemiesKilled: number;
  gameTimeMs: number;
  isMobile: boolean;
  canvasWidth: number;
  canvasHeight: number;
}

const GameHUD: React.FC<GameHUDProps> = ({
  score,
  level,
  lastLevel,
  enemiesKilled,
  gameTimeMs,
  isMobile,
  canvasWidth,
  canvasHeight,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [lastScore, setLastScore] = useState(score);
  const [scoreFlash, setScoreFlash] = useState(0);
  const [levelFlash, setLevelFlash] = useState(0);

  useEffect(() => {
    if (score > lastScore) {
      setScoreFlash(Date.now());
      setLastScore(score);
    }
  }, [score, lastScore]);

  useEffect(() => {
    if (level > lastLevel) {
      setLevelFlash(Date.now());
    }
  }, [level, lastLevel]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    let animationFrameId: number;

    const render = () => {
      const now = Date.now();
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      const scoreTime = now - scoreFlash;
      const levelTime = now - levelFlash;

      const scoreFlashIntensity = scoreTime < 200 ? Math.sin((scoreTime / 200) * Math.PI) : 0;
      const levelFlashIntensity = levelTime < 1000 ? Math.sin((levelTime / 1000) * Math.PI) : 0;
      const continuousGlow = 0.5 + Math.sin(now / 300) * 0.5;

      const drawText = (text: string, x: number, y: number, size: number, align: 'left' | 'center' | 'right', flashIntensity: number = 0) => {
        const baseGlow = 5;
        const flashGlow = 25;
        const glow = baseGlow * continuousGlow + (flashGlow * flashIntensity);
        const textColor = flashIntensity > 0 ? `rgba(255, 255, 255, ${0.8 + flashIntensity * 0.2})` : 'var(--red-secondary-ff4444)';
        
        ctx.font = `${size}px VT323`;
        ctx.textAlign = align;

        ctx.fillStyle = 'rgba(239, 68, 68, 0.9)';
        ctx.fillText(text, x, y);

        ctx.fillStyle = textColor;
        ctx.shadowColor = 'var(--red-main-ff0000)';
        ctx.shadowBlur = glow;
        ctx.fillText(text, x, y);
        
        ctx.shadowBlur = 0;
      };

      if (isMobile) {
        const fontSize = 16;
        const padding = 10;
        const yPos = 25;
        
        // Left - Score
        drawText(`SCORE: ${score}`, padding, yPos, fontSize, 'left', scoreFlashIntensity);
        
        // Center - Level and Kills
        const centerText = `LVL: ${level} | KILLS: ${enemiesKilled}`;
        drawText(centerText, canvasWidth / 2, yPos, fontSize, 'center', levelFlashIntensity);
        
        // Right - Time
        const totalSeconds = Math.floor(gameTimeMs / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        drawText(`TIME: ${formattedTime}`, canvasWidth - padding, yPos, fontSize, 'right');
      } else {
        const fontSize = 30;
        const yPos = 35;
        const padding = 20;

        drawText(`SCORE: ${score}`, padding, yPos, fontSize, 'left', scoreFlashIntensity);
        
        const centerText = `LVL: ${level} | KILLS: ${enemiesKilled}`;
        drawText(centerText, canvasWidth / 2, yPos, fontSize, 'center', levelFlashIntensity);
        
        const totalSeconds = Math.floor(gameTimeMs / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        drawText(`TIME: ${formattedTime}`, canvasWidth - padding, yPos, fontSize, 'right');
      }
      animationFrameId = requestAnimationFrame(render);
    };

    render();

    return () => {
      cancelAnimationFrame(animationFrameId);
    };

  }, [score, level, enemiesKilled, gameTimeMs, isMobile, canvasWidth, canvasHeight, scoreFlash, lastScore, levelFlash, lastLevel]);

  return (
    <canvas
      ref={canvasRef}
      width={canvasWidth}
      height={canvasHeight}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        zIndex: 100, // High z-index to ensure it's on top of everything
        pointerEvents: 'none', // Make it non-interactive
      }}
    />
  );
};

export default GameHUD;