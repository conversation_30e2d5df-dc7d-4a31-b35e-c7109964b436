export interface Upgrade {
  id: 'bulletSpread' | 'healthyBastard' | 'vampireGift' | 'explosiveJohnny' | 'solInvictus' | 'greedIsGood';
  name: string;
  description: (level: number) => string;
  maxLevel: number;
}

export const UPGRADES: Upgrade[] = [
  {
    id: 'bulletSpread',
    name: 'Bullet Spread',
    description: (level) => `+${level}% chance for a x3 bullet spread.`,
    maxLevel: 10,
  },
  {
    id: 'healthyBastard',
    name: 'Healthy Bastard',
    description: (level) => `+${level * 10} to max health for this run.`,
    maxLevel: 10,
  },
  {
    id: 'vampireGift',
    name: 'Vampire Gift',
    description: (level) => `+${(level * 0.1).toFixed(1)}% chance on hit to heal 10 HP.`,
    maxLevel: 10,
  },
  {
    id: 'explosiveJohnny',
    name: 'Explosive Johnny',
    description: (level) => `+${level * 0.5}% chance for bullets to explode, dealing 25% damage in a small radius.`,
    maxLevel: 10,
  },
  {
    id: 'solInvictus',
    name: '<PERSON> Invictus',
    description: (level) => `After taking damage, gain invincibility for +${level * 0.25}s.`,
    maxLevel: 2,
  },
  {
    id: 'greedIsGood',
    name: 'Greed is Good',
    description: (level) => `Increases score from defeated enemies by ${level * 2}%.`,
    maxLevel: 10,
  },
];