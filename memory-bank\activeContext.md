# Active Context: Blue Sky Invaders

## Critical Focus: Mobile Adaptation

### Current Challenge
- 800x600 aspect ratio optimal for desktop but problematic on mobile
- Direct vertical conversion would require enemy pattern redesign
- Need to maintain consistent difficulty across platforms

### Proposed Solution: Hybrid Layout Approach
1. **Core Game Area**
   - Maintain 800x600 gameplay logic
   - Consistent enemy patterns and difficulty
   - Platform-agnostic game rules

2. **Extended Viewport**
   - Strategic use of additional screen space
   - Off-screen threat indicators
   - Optimized UI placement
   - Enhanced touch controls

3. **Implementation Priority**
   - Viewport management system
   - Mobile-specific UI adaptations
   - Touch input optimization
   - Performance improvements

## Current Development Focus

### Recent Changes
1. **Enemy System Extraction** (Last Updated: 2025-06-30)
   - **NEW MODULE**: Created `enemySystem.tsx` (~330 lines) for dedicated enemy management
   - Extracted all enemy-related functionality: creation, spawning, movement, AI
   - Implemented `useEnemySystem` hook with dependency injection pattern
   - Prepared foundation for enemy pattern development and special behaviors
   - Maintained all existing enemy types: basic, fast, heavy, boss with full AI patterns

2. **Major Code Restructuring** (Last Updated: 2025-06-30)
   - **BREAKING CHANGE**: SpaceInvaders.tsx split into 3 focused files
   - Extracted game logic to `SpaceInvadersGameLogic.tsx` (~500 lines)
   - Extracted rendering functions to `SpaceInvadersRenderer.tsx` (~580 lines)
   - Main component reduced from ~1400 to ~1040 lines
   - Implemented dependency injection pattern for better modularity

2. **Enemy Spawning Adjustments** (Last Updated: 2025-06-24)
   - Reduced minimum spawn interval to 600ms
   - Increased maximum regular enemy cap to 25
   - Enhanced late-game intensity

3. **Diagonal Enemy Bullets** (Last Updated: 2025-06-24)
   - Added for Fast and Heavy enemies from level 21
   - 7.5% effective chance for diagonal shots
   - Improved bullet movement system

4. **Point System Enhancement** (Last Updated: 2025-06-24)
   - Added +5 points for power-up collection
   - Integrated with score system

## Active Development Patterns

### Ship Morphing Feature
- Visual changes based on powerLevel and fireRate
- Component-based rendering system
- Dynamic color adjustment
- Scale-aware rendering

### Game Balance Considerations
1. **Enemy Scaling**
   - Progressive difficulty through spawn rates
   - Enemy type distribution
   - Boss encounter frequency

2. **Power-up System**
   - Duration balancing
   - Effect stacking rules
   - Drop rate optimization

## Planned Features

### High Priority
1. **Echo Projectiles**
   - Temporary damage zones
   - Visual effects integration
   - Balance testing needed

2. **Momentum Shield**
   - Movement-based defense
   - Visual feedback system
   - Performance optimization required

### Under Consideration
1. **Temporal Echo Upgrade**
   - Time manipulation mechanics
   - Performance impact assessment
   - Visual effect design

2. **Adaptive Armor**
   - Dynamic defense system
   - Projectile type analysis
   - UI feedback design

## Active Learnings

### Technical Insights
1. **Canvas Performance**
   - Object pooling benefits
   - Render optimization techniques
   - Mobile performance considerations

2. **Game Balance**
   - Power curve progression
   - Difficulty scaling effectiveness
   - Player feedback integration

### Player Experience
1. **Control Responsiveness**
   - Platform-specific adjustments
   - Input lag minimization
   - Mobile touch precision

2. **Visual Feedback**
   - Power-up clarity
   - Damage indication
   - Status effect visibility

## Current Challenges

### Technical
1. **Mobile Performance**
   - Optimization needed for complex effects
   - Touch input precision
   - Battery consumption

2. **Collision Detection**
   - Efficiency improvements needed
   - Edge case handling
   - Diagonal bullet interactions

### Gameplay
1. **Late Game Balance**
   - Enemy density management
   - Power level progression
   - Boss difficulty scaling

2. **Power-up Distribution**
   - Drop rate tuning
   - Effect duration balance
   - Stacking mechanics

## Next Steps

### Immediate Actions
1. Implement Echo Projectiles feature
2. Test and balance diagonal bullets
3. Optimize mobile performance
4. Update visual feedback systems

### Future Considerations
1. Seasonal event system
2. Additional power-up types
3. Enhanced social features
4. Achievement system implementation

## Project Insights

### Successful Patterns
1. Component-based rendering
2. Manager-based game systems
3. Progressive difficulty scaling
4. Visual feedback systems

### Areas for Improvement
1. Mobile performance optimization
2. Power-up system complexity
3. Late-game balance
4. Social feature integration

## Documentation Needs
1. ✅ Update technical architecture docs (completed with restructuring)
2. Document new modular architecture patterns
3. Create development guidelines for new file structure
4. Update game rules documentation
5. Document new feature implementations
6. Create mobile-specific guidelines

## New Architecture Guidelines (Post-Restructuring)

### File Structure Overview
- **SpaceInvaders.tsx**: Main component, state management, UI rendering
- **SpaceInvadersGameLogic.tsx**: Core game logic, collision detection, entity updates
- **SpaceInvadersRenderer.tsx**: Canvas rendering, visual effects, CRT overlay
- **enemySystem.tsx**: Enemy management, spawning, movement, AI patterns

### Planned Future Extractions
- **combatSystem.tsx** (~200-300 lines): Bullet management, collision detection, damage calculations
- **playerSystem.tsx** (~150-200 lines): Player movement, upgrades, power-ups, health management
- **gameEffects.tsx** (~100-150 lines): Particle systems, visual effects, screen shake

### Development Guidelines
1. **Enemy Logic Changes**: Add new enemy behaviors to `enemySystem.tsx`
2. **Game Logic Changes**: Add core game mechanics to `SpaceInvadersGameLogic.tsx`
3. **Visual Effects**: Add new rendering features to `SpaceInvadersRenderer.tsx`
4. **UI Components**: Add new UI elements to main `SpaceInvaders.tsx`
5. **Dependency Injection**: Use established patterns for passing dependencies between modules
