import React, { useState, useEffect, useCallback } from 'react';
import { GameSettings } from '../../SpaceInvaders';

interface PauseMenuProps {
  isPaused: boolean;
  onResume: () => void;
  onSettings: () => void;
  onQuit: () => void;
}

type GameState = 'menu' | 'playing' | 'gameOver' | 'options' | 'submitScore' | 'leaderboard' | 'upgrading';

// Define a more specific type for the audioRefs object expected by usePauseGame
interface AudioRefsForPause {
  backgroundMusic: HTMLAudioElement | null;
  // Add other sounds if needed by this hook in the future, but currently only backgroundMusic is used.
}

export const usePauseGame = (
  gameState: GameState,
  audioRefs: React.RefObject<AudioRefsForPause>, // Changed audioRef to audioRefs and updated type
  settings: GameSettings,
  onResumeCallback?: () => void // Optional callback when resuming from pause
) => {
  const [isPaused, setIsPaused] = useState(false);

  const togglePause = useCallback(() => {
    if (gameState === 'playing') {
      setIsPaused(prev => {
        if (prev && onResumeCallback) { // If it was paused (true), it's now resuming
          onResumeCallback();
        }
        return !prev;
      });
    }
  }, [gameState, onResumeCallback]);

  // Handle keyboard pause functionality
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'p' || e.key === 'P') {
        togglePause();
      }
    };

    if (gameState === 'playing') {
      window.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [gameState, togglePause]);

  // Effect to handle pausing/resuming background music
  useEffect(() => {
    if (audioRefs.current?.backgroundMusic && settings.musicEnabled) {
      if (isPaused && gameState === 'playing') {
        audioRefs.current.backgroundMusic.pause();
      } else if (!isPaused && gameState === 'playing') {
        // Attempt to play, catch error if user hasn't interacted yet
        audioRefs.current.backgroundMusic.play().catch(() => {
          // console.warn("Background music play failed, possibly due to no user interaction yet.");
        });
      }
    }
  }, [isPaused, gameState, audioRefs, settings.musicEnabled]);

  return {
    isPaused,
    setIsPaused,
    togglePause
  };
};

export const PauseMenu: React.FC<PauseMenuProps> = ({ isPaused, onResume, onSettings, onQuit }) => {
  if (!isPaused) return null;

  return (
    <div className="pauseBtn fixed inset-0 flex items-center justify-center z-[110] bg-black bg-opacity-70">
      <div className="bg-black border-2 border-red-500 p-6 max-w-md w-full text-red-500 shadow-xl shadow-red-500/20">
        <h2 className="text-3xl font-bold mb-6 text-center">PAUSED</h2>
        
        <div className="space-y-4">
          <button 
            onClick={onResume}
            className="block w-full py-3 border-2 border-red-500 text-xl font-bold hover:bg-red-500 hover:text-black transition-colors"
          >
            RESUME
          </button>
          
          <button 
            onClick={onSettings}
            className="block w-full py-3 border-2 border-red-500 text-xl font-bold hover:bg-red-500 hover:text-black transition-colors"
          >
            SETTINGS
          </button>
          
          <button 
            onClick={onQuit}
            className="block w-full py-3 border-2 border-red-500 text-xl font-bold hover:bg-red-500 hover:text-black transition-colors"
          >
            QUIT TO MENU
          </button>
        </div>
        
        <p className="mt-6 text-center text-sm">
          Press <span className="text-red-400 font-bold">P</span> to resume
        </p>
      </div>
    </div>
  );
};

export const useGameControls = (
  settings: GameSettings
) => {
  const keyMap = useCallback(() => {
    return {
      left: settings.controlScheme === 'wasd' ? 'a' : 'ArrowLeft',
      right: settings.controlScheme === 'wasd' ? 'd' : 'ArrowRight',
      shoot: ' ',  // Space
      pause: 'p',  // P key
      heal: 'h',   // H key
    };
  }, [settings.controlScheme]);

  return {
    keyMap,
    getMovementKeys: () => {
      if (settings.controlScheme === 'wasd') {
        return { left: 'a', right: 'd', shoot: ' ' };
      }
      return { left: 'ArrowLeft', right: 'ArrowRight', shoot: ' ' };
    }
  };
};

export const PauseButton: React.FC<{ onClick: () => void }> = ({ onClick }) => {
  return (
    <button 
      onClick={onClick}
      className="pauseBtn absolute top-[50px] sm:hidden top-4 right-4 text-red-500 border-2 border-red-500 px-4 py-1 hover:bg-red-500 hover:text-black transition-colors"
      aria-label="Pause game"
    >
      PAUSE (P)
    </button>
  );
};
