## Proposed Incremental Upgrades & Implementation Plan
Here are six features that build upon the existing game systems to add more depth, variety, and player engagement.

### 1. Streak-Based Score Bonus
Concept: Reward players with a cascading score bonus for destroying multiple enemies in a row without taking damage. This encourages skillful play and risk/reward assessment.

Implementation Guide for AI:
File(s) to Modify: src/SpaceInvaders.tsx
Key Functions/Interfaces: Player interface, updateGame function.

Logic Steps:
Update Player State: Add a killStreak: number property to the Player interface in SpaceInvaders.tsx. Initialize it to 0 in the startGame function.
Increment Streak: In the updateGame function, within the loop where player bullets collide with enemies and an enemy is destroyed, increment gameStateRef.current.player.killStreak.

Display Streak Bonus: When the streak is incremented, check if killStreak has reached a milestone (e.g., 10, 25, 50). If it has, add a bonus to the score (setScore) and create a FloatingText element (e.g., "+50 Streak Bonus!") at the player's location to provide visual feedback.

Reset Streak: In the section of updateGame where an enemy bullet collides with the player, reset gameStateRef.current.player.killStreak to 0.

### 2. New Power-Up: Shield
Concept: A new, rare power-up that grants the player a single-hit shield. This provides a valuable defensive tool, especially during intense boss fights or bullet-heavy waves.

Implementation Guide for AI:
File(s) to Modify: src/SpaceInvaders.tsx
Key Functions/Interfaces: Player interface, PowerUp interface, updateGame function, render function.

Logic Steps:
Update Interfaces: Add 'shield' as a possible type to the PowerUp interface. 
Add hasShield: boolean to the Player interface, initializing it to false.
Power-Up Spawning: In updateGame, add 'shield' to the array of possible power-ups that can be dropped by enemies.
Shield Activation: When the player collides with a 'shield' power-up, set gameStateRef.current.player.hasShield = true.

Shield Logic: In the collision logic where the player is hit by an enemy bullet, add a check: if player.hasShield is true, prevent the health damage, set player.hasShield = false, and create a particle effect to show the shield breaking. Do not reset the kill streak if only the shield is hit.

Visuals: In the render function, if player.hasShield is true, draw a semi-transparent circle or hexagon around the player ship to visually represent the shield.

### 3. New Enemy: The "Splitter"
Concept: An enemy that, upon destruction, splits into two smaller, faster, but weaker versions of the "fast" enemy type. This complicates target prioritization and screen control.

Implementation Guide for AI:
File(s) to Modify: src/SpaceInvaders.tsx
Key Functions/Interfaces: Enemy interface, createEnemy function, updateGame function.

Logic Steps:
Update Enemy Type: Add 'splitter' as a possible type to the Enemy interface.
Create Splitter Enemy: In the createEnemy function, add a case for the 'splitter' type with unique stats (e.g., slightly more health than a basic enemy, moderate points).

Spawning Logic: In spawnEnemies, add logic to start spawning 'splitter' enemies after a certain level (e.g., level > 10).
Splitting Mechanic: In updateGame, when a player bullet destroys an enemy, check if enemy.type === 'splitter'. If it is, call the createEnemy function twice to spawn two 'fast' enemies at the location where the splitter was destroyed. Give them a slight initial horizontal velocity (vx) in opposite directions to separate them.

### 4. Environmental Hazard: Asteroid Field
Concept: Periodically, a field of destructible asteroids drifts down from the top of the screen. They are neutral, blocking both player and enemy shots, and will damage the player on collision.

Implementation Guide for AI:
File(s) to Modify: src/SpaceInvaders.tsx
Key Functions/Interfaces: GameInternalState interface, updateGame, render.

Logic Steps:
Create Asteroid State: Define a new Asteroid interface (with x, y, width, height, vy, health). Add an asteroids: Asteroid[] array to the GameInternalState interface.

Spawn Asteroids: Create a new function, spawnAsteroids(currentTime), called from within updateGame. This function should, on a timer (e.g., every 15-20 seconds), create a wave of 5-8 asteroids at random X positions above the screen.

Update Logic: In updateGame, add a loop to move the asteroids down the screen (asteroid.y += asteroid.vy).

Collision Logic:
Check for collisions between player bullets and asteroids. On collision, damage the asteroid's health and remove the bullet. If asteroid health is <= 0, remove it and create a particle explosion.
Check for collisions between enemy bullets and asteroids (same logic).
Check for collisions between the player and asteroids. On collision, damage the player and destroy the asteroid.

Render Asteroids: In the render function, loop through state.asteroids and draw them (e.g., as grey, jagged polygons or circles).

### 5. Enemy "Enrage" Mechanic
Concept: When only one non-boss enemy remains on screen, it "enrages," gaining a temporary but significant boost to its movement speed and fire rate. This prevents lulls in the action and makes the final enemy of a wave a genuine threat.

Implementation Guide for AI:
File(s) to Modify: src/SpaceInvaders.tsx
Key Functions/Interfaces: Enemy interface, updateGame function.

Logic Steps:
Update Enemy State: Add an optional isEnraged: boolean property to the Enemy interface.

Enrage Detection: In updateGame, near the beginning of the function, check the number of enemies. If state.enemies.length === 1 &amp;&amp; state.enemies[0].type !== 'boss', and its isEnraged flag is not already true, set state.enemies[0].isEnraged = true.
Apply Enrage Buffs: In the sections where enemy movement (e.y += e.vy) and firing logic are handled, add a check: if e.isEnraged, multiply its speed and decrease its fire rate delay by a factor (e.g., speed * 1.5, fire rate delay / 2).

Visual Cue: In the render function, if an enemy isEnraged, draw it with a different, more intense color (e.g., a brighter, pulsating red) to signal its enhanced state to the player.

### 6. Persistent Currency: "Scrap" Collection
Concept: Introduce a persistent currency, "Scrap," that is dropped by enemies and collected by the player. This currency will be saved between game sessions and will be used for permanent upgrades in a future "Hangar" screen. This task only covers the collection and persistence of the currency.

Implementation Guide for AI:
File(s) to Modify: src/SpaceInvaders.tsx, convex/schema.ts, convex/index.ts (or a new users.ts file).

Logic Steps:
Database Schema: In convex/schema.ts, create a new table called users. It should store data by the user's DID (.index("by_did", ["did"])) and include a field for scrap: v.number().

Backend Functions: Create two new Convex mutations:
getUserData: Takes a DID and returns the user's document, creating a new one with scrap: 0 if it doesn't exist.
addScrap: Takes a DID and an amount, finds the user's document, and increments their scrap total.

Scrap Drop Logic: In src/SpaceInvaders.tsx, when an enemy is destroyed, have it drop a "Scrap" pickup (similar to a power-up) with a certain probability. The amount of scrap could vary by enemy type.
Scrap Collection: When the player collides with the Scrap pickup, instead of applying an immediate effect, call the addScrap mutation with the player's DID (if logged in) and the scrap amount.

UI Display (Optional but Recommended): Add a small, non-interactive display on the main game UI to show the total scrap collected during the current run. The persistent total would be shown in the future Hangar screen.