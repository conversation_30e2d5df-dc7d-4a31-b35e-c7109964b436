import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  scores: defineTable({
    playerName: v.string(),
    score: v.number(),
    level: v.optional(v.number()),          // Changed to optional
    enemiesKilled: v.optional(v.number()),  // Changed to optional
    gameDurationMs: v.optional(v.number()), // Added
    isAnonymous: v.optional(v.boolean()),   // Added
    blueskyDid: v.optional(v.string()),
    blueskyHandle: v.optional(v.string()),
    challengedHandles: v.optional(v.array(v.string())),
    timestamp: v.optional(v.number()), // Optional for backward compatibility
    rank: v.optional(v.number()), // Track position in high scores
  })
  .index("by_score", ["score", "level", "enemiesKilled"]) // Consider if gameDurationMs should be in an index
  .index("by_blueskyHandle_score", ["blueskyHandle", "score"]),
});
