import { Toaster } from "sonner";
import { useState, useRef } // Added useState, useRef
from 'react'; 
import SpaceInvaders, { GameState as SpaceInvadersGameState } from "./SpaceInvaders"; // Import GameState
import AnimatedBackground from "./components/AnimatedBackground";
import DisplayModeManager from './components/gameUI/DisplayModeManager.tsx'; // Added .tsx extension

export default function App() {
  const gameWrapperRef = useRef<HTMLDivElement>(null);
  const [isMaximized, setIsMaximized] = useState(false); // Renamed
  const [currentGameStateForDisplayManager, setCurrentGameStateForDisplayManager] = useState<SpaceInvadersGameState>('menu'); // Explicitly type state

  let initialDevStateProp: SpaceInvadersGameState | undefined = undefined; // Use GameState type
  if (import.meta.env.DEV) {
    const devStartFlag = localStorage.getItem('devForceStartGame') === 'true';
    if (devStartFlag) {
      initialDevStateProp = 'playing';
      // Optional: Clear the flag after reading it so it doesn't persist on next manual reload
      // localStorage.removeItem('devForceStartGame');
    }
  }


  return (
    <div className="h-screen flex flex-col" style={{ fontFamily: 'VT323, monospace' }}>
      <AnimatedBackground color="rgba(239, 68, 68, 1)" intensity={1} />
      <header className="hidden fixed w-full top-0 z-10 bg-black/80 backdrop-blur-sm h-16 flex justify-center items-center border-b border-[var(--red-main-ff0000)] shadow-sm px-4">
        <h2 className="text-xl font-semibold text-[var(--red-main-ff0000)]">RETRO ARCADE</h2>
      </header>      
      <main className={`flex flex-1 items-center justify-center relative ${isMaximized ? 'p-0' : 'lg:px-8'}`}> {/* Adjust padding when maximized */}
        <DisplayModeManager
          gameWrapperRef={gameWrapperRef} // gameWrapperRef might not be needed by DisplayModeManager anymore
          isMaximized={isMaximized} // Renamed
          // setIsMaximized={setIsMaximized} // DisplayModeManager only needs to toggle
          onToggleMaximized={() => setIsMaximized(prev => !prev)} // Renamed
          orientation="horizontal" // Placeholder for Phase 1
          setOrientation={() => console.log("Set orientation placeholder")} // Placeholder for Phase 1
          currentGameState={currentGameStateForDisplayManager}
        />
        <div 
          ref={gameWrapperRef} 
          className={`relative mx-auto flex items-center justify-center transition-all duration-300 ease-in-out
              ${isMaximized 
                ? 'fixed inset-0 w-screen h-screen bg-black z-40' // Maximized styles (z-40 to be below DisplayModeManager's z-50)
                : 'w-full max-w-screen-lg z-10' // Normal styles
              }`}
        >
          <SpaceInvaders 
            initialDevState={initialDevStateProp} 
            onGameStateChange={setCurrentGameStateForDisplayManager}
            isMaximized={isMaximized} // Pass isMaximized state
            setIsMaximized={setIsMaximized} // Pass the state setter for isMaximized
          />
        </div>
      </main>
      <Toaster />
    </div>
  );
}
