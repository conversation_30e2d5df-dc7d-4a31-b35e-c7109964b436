import React, { useEffect, useRef } from 'react';

interface AnimatedBackgroundProps {
  intensity?: number; // Controls animation speed/density
  color?: string; // Main color for animations
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  intensity = 1,
  color = 'rgba(239, 68, 68, 1)', // Default red color
}) => {
  // Animation state references
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const animationFrameIdRef = useRef<number | null>(null);
  const horizontalIntervalIdRef = useRef<number | null>(null);
  const horizontalArrayRef = useRef<any[]>([]);
  const verticalArrayRef = useRef<any[]>([]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const c = canvas.getContext('2d');
    if (!c) return;

    // Set canvas size to match window
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    let grad: CanvasGradient | null = null;

    // Horizontal line constructor
    function Horizontal(this: any, y: number) {
      this.y = y;
      this.dy = 0.5 * intensity; // Use intensity prop
      this.opacity = 0;

      this.draw = () => {
        if (!c || !canvas) return;
        
        c.beginPath();
        c.lineWidth = 3;
        // Use provided color with opacity
        const baseColor = color.startsWith('rgba') ? color.replace(/rgba?\([^,]+,\s*[^,]+,\s*[^,]+,\s*[^)]+\)/, 
          (match) => match.replace(/\d+(\.\d+)?(?=\))/, String(this.opacity))) : 
          `rgba(239, 68, 68, ${this.opacity})`;
          
        c.strokeStyle = baseColor;
        c.moveTo(0, this.y);
        c.lineTo(canvas.width, this.y);
        c.stroke();
      };

      this.update = () => {
        if (!canvas) return;
        
        // Remove if off screen
        if (this.y >= canvas.height) {
          const currentArray = horizontalArrayRef.current;
          const index = currentArray.indexOf(this);
          if (index !== -1) {
            currentArray.splice(index, 1);
          }
          return;
        }

        // Increase opacity and move down
        this.opacity += 0.003 * intensity;
        if (this.opacity > 1) this.opacity = 1;
        this.dy += 0.05 * intensity;
        this.y += this.dy;
        this.draw();
      };
    }

    // Vertical line constructor
    function Vertical(this: any, x: number) {
      this.x = x;

      this.draw = () => {
        if (!c || !canvas || !grad) return;
        
        c.beginPath();
        c.lineWidth = 1;
        c.strokeStyle = grad;
        c.moveTo(this.x, 0);
        c.lineTo(this.x, canvas.height);
        c.stroke();
      };

      this.update = () => {
        this.draw();
      };
    }

    const setupVerticalsAndGradient = () => {
      if (!c || !canvas) return;
      
      // Create gradient for vertical lines
      grad = c.createLinearGradient(0, canvas.height, 0, 0);
      
      // Extract base RGB components from the color prop
      let r = 239, g = 68, b = 68; // Default values
      const colorMatch = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/);
      if (colorMatch) {
        r = parseInt(colorMatch[1], 10);
        g = parseInt(colorMatch[2], 10);
        b = parseInt(colorMatch[3], 10);
      }
      
      grad.addColorStop(0, `rgba(${r}, ${g}, ${b}, 0.5)`);
      grad.addColorStop(0.55, `rgba(${r}, ${g}, ${b}, 0)`);
      grad.addColorStop(1.0, `rgba(${r}, ${g}, ${b}, 0)`);

      verticalArrayRef.current = [];
      const interval = canvas.width / 10;
      let cross = 0 - interval * 8;
      
      for (let i = 0; i < 27; i++) {
        verticalArrayRef.current.push(new (Vertical as any)(cross));
        cross += interval;
      }
    };

    const handleResize = () => {
      if (!canvas || !c) return;
      
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      setupVerticalsAndGradient();
    };

    setupVerticalsAndGradient();
    window.addEventListener('resize', handleResize);

    // Add new horizontal lines periodically
    horizontalIntervalIdRef.current = window.setInterval(() => {
      if (!canvas) return;
      
      horizontalArrayRef.current.push(new (Horizontal as any)(canvas.height / 2));
    }, 300 / intensity); // Frequency adjusted by intensity

    // Animation loop
    const animate = () => {
      if (!c || !canvas) return;
      
      animationFrameIdRef.current = requestAnimationFrame(animate);
      c.clearRect(0, 0, canvas.width, canvas.height);

      // Update horizontal lines
      for (let i = 0; i < horizontalArrayRef.current.length; i++) {
        horizontalArrayRef.current[i].update();
      }
      
      // Update vertical lines
      for (let i = 0; i < verticalArrayRef.current.length; i++) {
        verticalArrayRef.current[i].update();
      }
    };
    
    animate();

    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', handleResize);
      
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
        animationFrameIdRef.current = null;
      }
      
      if (horizontalIntervalIdRef.current) {
        clearInterval(horizontalIntervalIdRef.current);
        horizontalIntervalIdRef.current = null;
      }
      
      horizontalArrayRef.current = [];
      verticalArrayRef.current = [];
    };
  }, [intensity, color]);

  return (
    <canvas 
      ref={canvasRef} 
      style={{ 
        position: 'fixed', // Use fixed to stay in place when scrolling
        top: 0, 
        left: 0, 
        width: '100%', 
        height: '100%', 
        zIndex: -1, // Place behind all content
        background: '#000',
        pointerEvents: 'none', // Prevents capturing mouse events
      }} 
      aria-hidden="true" // Hide from assistive technology
    />
  );
};

export default AnimatedBackground;
