// src/gameLogic/difficultyManager.ts

/**
 * Checks if the player should level up based on enemies killed and current level.
 * New logic: enemiesKilled % (level * 15 + 15) === 0
 * @param enemiesKilled Total number of enemies killed by the player.
 * @param currentLevel The player's current level.
 * @returns True if the player should level up, false otherwise.
 */
export function checkPlayerLevelUp(enemiesKilled: number, currentLevel: number): boolean {
  if (enemiesKilled <= 0 || currentLevel <= 0) {
    return false;
  }
  // Ensure the divisor is not zero if level is manipulated unexpectedly, though currentLevel starts at 1.
  const divisor = currentLevel * 15 + 15;
  if (divisor === 0) return false; // Should not happen with level >= 1
  return enemiesKilled % divisor === 0;
}

/**
 * Gets the amount of health the player should gain upon leveling up.
 * Current logic: +10 health.
 * @returns The amount of health to add.
 */
export function getPlayerHealthBoostOnLevelUp(): number {
  return 10;
}

/**
 * Calculates the number of enemies to spawn in the current wave based on the player's level.
 * New logic: Math.min(3 + Math.floor(level / 5), 12)
 * @param currentLevel The player's current level.
 * @returns The number of enemies to spawn.
 */
export function getEnemySpawnCount(currentLevel: number): number {
  // 1 new enemy slot every 5 levels, cap 12. Starts with 3.
  return Math.min(3 + Math.floor(currentLevel / 5), 25); // Increased cap from 12 to 25
}

export const INACTIVITY_CONSTANTS = {
  BLINK_THRESHOLD_S: 4, // Player starts blinking after 2 seconds of inactivity
  PENALTY_START_THRESHOLD_S: 5, // Penalty damage starts after 3 seconds
  SIGNIFICANT_MOVE_PX: 100, // Min horizontal move to reset inactivity
  // Damage for each second of penalty: 3rd sec=10, 4th=20, ..., 8th=100, 9th+=100
  // Index 0 = 3rd second of total inactivity (1st second of penalty)
  // Index 5 = 8th second of total inactivity (6th second of penalty, 100 damage)
  // Damage caps at 100 for subsequent seconds.
  PENALTY_DAMAGE_TIERS: [10, 20, 40, 60, 80, 100], 
};

/**
 * Calculates inactivity penalties and visual state for the player.
 * @param timeStationaryMs Milliseconds the player has been effectively stationary.
 * @param lastAppliedPenaltyTier The tier (e.g., 3 for 3rd second, 4 for 4th second) for which penalty was last applied.
 * @returns An object with healthPenalty, shouldBlink flag, and the new lastAppliedPenaltyTier.
 */
export function getInactivityState(
  timeStationaryMs: number,
  lastAppliedPenaltyTier: number 
): { healthPenalty: number; shouldBlink: boolean; newLastAppliedPenaltyTier: number } {
  const timeStationarySeconds = Math.floor(timeStationaryMs / 1000);
  let healthPenalty = 0;
  const shouldBlink = timeStationarySeconds >= INACTIVITY_CONSTANTS.BLINK_THRESHOLD_S;
  let newLastAppliedPenaltyTier = lastAppliedPenaltyTier;

  if (timeStationarySeconds >= INACTIVITY_CONSTANTS.PENALTY_START_THRESHOLD_S) {
    const currentSecondTierKey = timeStationarySeconds; // Represents the Nth second of total inactivity (e.g., 3, 4, 5...)
    
    if (currentSecondTierKey > lastAppliedPenaltyTier) {
      // Index for PENALTY_DAMAGE_TIERS (0-indexed for PENALTY_START_THRESHOLD_S onwards)
      const damageTierIndex = Math.min(
        currentSecondTierKey - INACTIVITY_CONSTANTS.PENALTY_START_THRESHOLD_S,
        INACTIVITY_CONSTANTS.PENALTY_DAMAGE_TIERS.length - 1 // Cap index to last defined tier
      );
      healthPenalty = INACTIVITY_CONSTANTS.PENALTY_DAMAGE_TIERS[damageTierIndex];
      newLastAppliedPenaltyTier = currentSecondTierKey;
    }
  }
  return { healthPenalty, shouldBlink, newLastAppliedPenaltyTier };
}
