# Product Context

## Problem Statement
Create an engaging, multiplayer version of the classic Space Invaders game that maintains the essence of the original while adding modern features like real-time gameplay and Bluesky social integration.

## User Experience Goals
1. **Authentic Gameplay**: Recreate the classic Space Invaders experience
2. **Modern Features**: Add multiplayer capabilities and <PERSON>ky handle verification
3. **Responsive Controls**: Ensure smooth, responsive game controls
4. **Audio Feedback**: Provide engaging sound effects and background music
5. **Social Features**: Enable competition through high scores linked to Bluesky identities

## Target Users
- Retro gaming enthusiasts
- Casual gamers looking for quick, engaging gameplay
- Players interested in competitive gaming
- Bluesky social network users

## Key Features
1. **Core Gameplay**
   - Player ship movement and shooting
   - Enemy formations and behavior
   - Collision detection
   - Scoring system

2. **Multiplayer Features**
   - Real-time game state synchronization
   - Player interactions
   - Competitive leaderboards

3. **Score Submission System**
   - Bluesky handle verification
   - Optional anonymous submission
   - High score leaderboard with pagination (100 scores per page, up to 1000 total results)
   - Player identity verification through Bluesky API
   - Search functionality by <PERSON><PERSON> handle
   - Comprehensive score history tracking
   - Automated top-rank announcements for top 3 players

4. **Player Upgrade System**
   - **Roguelike Progression**: On level-up, the player is presented with a choice of three randomly selected upgrades to acquire or improve for the current run.
   - **Available Upgrades**:
     - **Bullet Spread**: Adds a percentage chance for a powerful 3-way shot.
     - **Healthy Bastard**: Increases the player's maximum health.
     - **Vampire Gift**: Gives a percentage chance on hit to heal the player.

5. **Audio System**
   - Background music
   - Game sound effects (shooting, explosions, etc.)
   - Audio control options
