// src/dev-utils.ts

/**
 * Enables "God Mode" by setting a flag in localStorage.
 * In God Mode, player health resets upon death instead of game over.
 * Intended for development use via browser console.
 */
export function enableGodMode(): void {
  localStorage.setItem('devGodMode', 'true');
  console.log('[DevUtils] God Mode enabled. Player will be invincible (health resets on death).');
  // No reload needed if already in game, will take effect on next death check.
}

/**
 * Disables "God Mode" by removing the flag from localStorage.
 * Intended for development use via browser console.
 */
export function disableGodMode(): void {
  localStorage.removeItem('devGodMode');
  console.log('[DevUtils] God Mode disabled. Normal death rules apply.');
}

/**
 * Sets a flag in localStorage to indicate that the game should start immediately
 * in 'playing' state on the next load, then reloads the page.
 * Intended for development use via browser console.
 */
export function forceStartGame(): void {
  localStorage.setItem('devForceStartGame', 'true');
  console.log('[DevUtils] devForceStartGame flag set. Reloading...');
  window.location.reload();
}

/**
 * Clears the flag that forces the game to start immediately, then reloads.
 * Intended for development use via browser console.
 */
export function clearForceStartGame(): void {
  localStorage.removeItem('devForceStartGame');
  console.log('[DevUtils] devForceStartGame flag cleared. Reloading...');
  window.location.reload();
}

// Expose to window object for easy console access during development
if (import.meta.env.DEV) {
  interface DevWindow extends Window {
    devForceStartGame?: () => void;
    devClearForceStartGame?: () => void;
    enableGodMode?: () => void;
    disableGodMode?: () => void;
  }
  (window as DevWindow).devForceStartGame = forceStartGame;
  (window as DevWindow).devClearForceStartGame = clearForceStartGame;
  (window as DevWindow).enableGodMode = enableGodMode;
  (window as DevWindow).disableGodMode = disableGodMode;

  console.log(
    '%c[DevUtils] Loaded. Call %cwindow.devForceStartGame()%c, %cwindow.devClearForceStartGame()%c, %cwindow.enableGodMode()%c, or %cwindow.disableGodMode()%c from console.',
    'color: orange; font-weight: bold;',
    'color: lightblue; font-family: monospace;',
    'color: orange; font-weight: bold;',
    'color: lightblue; font-family: monospace;',
    'color: orange; font-weight: bold;',
    'color: lightblue; font-family: monospace;',
    'color: orange; font-weight: bold;'
  );
}
