import { mutation, action } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";

const BSKY_API = "https://bsky.social/xrpc";

// Utility function to resolve handle to DID and verify it exists
async function resolveMentions(handles: string[]): Promise<Array<{ handle: string; did: string }>> {
  const mentions = [];
  for (const handle of handles) {
    try {
      const response = await fetch(`${BSKY_API}/com.atproto.identity.resolveHandle?handle=${handle}`);
      if (response.ok) {
        const data = await response.json();
        mentions.push({ handle, did: data.did });
      } else {
        console.error("Failed to resolve handle:", handle);
      }
    } catch (error) {
      console.error("Failed to resolve handle:", handle, error);
    }
  }
  return mentions;
}

// Utility function to create mention facets
async function createMentionFacets(text: string, mentions: Array<{ handle: string; did: string }>) {
  const facets = [];
  const encoder = new TextEncoder();
  
  for (const { handle, did } of mentions) {
    const mentionStr = `@${handle}`;
    const index = text.indexOf(mentionStr);
    if (index !== -1) {
      const byteStart = encoder.encode(text.substring(0, index)).length;
      const byteEnd = byteStart + encoder.encode(mentionStr).length;
      facets.push({
        index: { byteStart, byteEnd },
        features: [{
          $type: 'app.bsky.richtext.facet#mention',
          did
        }]
      });
    }
  }
  return facets;
}

// Utility function to create Bluesky session
async function getBlueskySession() {
  const identifier = process.env.BLUESKY_HANDLE;
  const password = process.env.BLUESKY_APP_PASSWORD;

  if (!identifier || !password) {
    console.error("Missing Bluesky credentials:", {
      hasIdentifier: !!identifier,
      hasPassword: !!password,
      identifierLength: identifier?.length,
      passwordLength: password?.length
    });
    throw new Error("Bluesky credentials not configured - check environment variables");
  }

  console.log("Attempting Bluesky login with:", {
    handle: identifier,
    passwordLength: password.length
  });

  const sessionResponse = await fetch(`${BSKY_API}/com.atproto.server.createSession`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ identifier, password }),
  });

  if (!sessionResponse.ok) {
    const errorData = await sessionResponse.json().catch(() => null);
    console.error("Bluesky Session Error:", {
      status: sessionResponse.status,
      statusText: sessionResponse.statusText,
      error: errorData,
      handle: identifier
    });
    throw new Error(`Failed to create Bluesky session: ${sessionResponse.status} ${sessionResponse.statusText}`);
  }

  const sessionData = await sessionResponse.json();
  console.log("Bluesky session created successfully for:", identifier);
  return sessionData;
}

// Utility function to post to Bluesky
async function postToBluesky(accessJwt: string, text: string, mentions: Array<{ handle: string; did: string }> = []) {
  if (!process.env.BLUESKY_DID) {
    throw new Error("BLUESKY_DID not configured in environment variables");
  }

  const response = await fetch(`${BSKY_API}/com.atproto.repo.createRecord`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessJwt}`,
    },
    body: JSON.stringify({
      repo: process.env.BLUESKY_DID,
      collection: "app.bsky.feed.post",
      record: {
        text,
        facets: await createMentionFacets(text, mentions),
        createdAt: new Date().toISOString(),
        $type: "app.bsky.feed.post",
      },
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => null);
    console.error("Bluesky API Error:", {
      status: response.status,
      statusText: response.statusText,
      error: errorData,
      did: process.env.BLUESKY_DID,
      handle: process.env.BLUESKY_HANDLE
    });
    throw new Error(`Failed to post to Bluesky: ${response.status} ${response.statusText}`);
  }
  console.log("Successfully posted to Bluesky:", { text });
}

// Public action for handle verification
export const verifyBlueskyHandle = action({
  args: {
    handle: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      if (!args.handle.endsWith('.bsky.social')) {
        return { valid: false, error: 'Handle must end with .bsky.social' };
      }

      const resolved = await resolveMentions([args.handle]);
      if (resolved.length === 0) {
        return { valid: false, error: 'Handle not found on Bluesky' };
      }
      
      return { 
        valid: true, 
        did: resolved[0].did,
        handle: resolved[0].handle
      };
    } catch (error) {
      return { valid: false, error: 'Failed to verify handle' };
    }
  },
});

// Post score to Bluesky
export const postScore = action({
  args: {
    scoreId: v.id("scores"),
  },
  handler: async (ctx, args) => {
    const score = await ctx.runQuery(api.index.getScore, { id: args.scoreId });
    if (!score) {
      throw new Error("Score not found");
    }

    if (!score.blueskyHandle) {
      throw new Error("Score has no associated Bluesky handle");
    }
    if (!score.blueskyDid) {
      throw new Error("Score has no associated Bluesky DID");
    }

    // Fetch the rank for this score
    const rank = await ctx.runQuery(api.index.getScoreRank, { scoreId: args.scoreId });

    const session = await getBlueskySession();
    
    const rankText = rank !== null ? ` Current rank: #${rank}.` : '';
    const postText = `💥 @${score.blueskyHandle} just dropped a score of ${score.score} in ARCADE INVADERS! They reached level ${score.level} and took down ${score.enemiesKilled} enemies.${rankText} Think you can beat that? 👉 https://bs-invaders.vercel.app #RetroGaming`;
    
    await postToBluesky(session.accessJwt, postText, [{ handle: score.blueskyHandle, did: score.blueskyDid }]);

    return { success: true };
  },
});

// Challenge action that validates handles
export const validateChallengeHandles = action({
  args: {
    challengedHandles: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    // Validate number of handles
    if (args.challengedHandles.length > 2) {
      throw new Error("Cannot challenge more than 2 players");
    }

    // Verify handles
    const resolvedHandles = await resolveMentions(args.challengedHandles);
    
    // Check if all handles were resolved
    if (resolvedHandles.length !== args.challengedHandles.length) {
      const failed = args.challengedHandles.filter(h => 
        !resolvedHandles.some(r => r.handle === h)
      );
      throw new Error(`Failed to resolve handles: ${failed.join(", ")}`);
    }

    return { 
      valid: true,
      dids: resolvedHandles.map(r => r.did)
    };
  },
});

// Post challenge after handle validation
export const postChallenge = action({
  args: {
    scoreId: v.id("scores"),
    challengedHandles: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    // Get score
    const score = await ctx.runQuery(api.index.getScore, { id: args.scoreId });
    if (!score) {
      throw new Error("Score not found");
    }

    // Get session and post challenge
    const session = await getBlueskySession();
    if (!score.blueskyHandle) {
      throw new Error("Score has no associated Bluesky handle");
    }
    // First resolve all handles to get their DIDs
    const resolved = await resolveMentions([score.blueskyHandle, ...args.challengedHandles]);
    const challengeText = `🔥 The gauntlet has been thrown! @${score.blueskyHandle} (score: ${score.score}) is calling out ${args.challengedHandles.map(h => `@${h}`).join(" and ")}! Can you beat their score in ARCADE INVADERS? 👾 👉 https://bs-invaders.vercel.app #ChallengeAccepted`;
    await postToBluesky(session.accessJwt, challengeText, resolved);

    // Update challenged handles
    await ctx.runMutation(api.index.updateScore, {
      id: args.scoreId,
      updates: { challengedHandles: args.challengedHandles }
    });

    return { success: true };
  },
});

// Update challenged handles (internal)
export const updateChallengedHandles = action({
  args: {
    scoreId: v.id("scores"),
    challengedHandles: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.runMutation(api.index.updateScore, {
      id: args.scoreId,
      updates: { challengedHandles: args.challengedHandles }
    });
    return { success: true };
  },
});

// Announce a top-rank achievement
export const postTopRankAnnouncement = action({
  args: {
    scoreId: v.id("scores"),
  },
  handler: async (ctx, args) => {
    // 1. Get the new score and the leaderboard
    const newScore = await ctx.runQuery(api.index.getScore, { id: args.scoreId });
    if (!newScore || !newScore.blueskyHandle || !newScore.blueskyDid) {
      console.error("Cannot announce score: score not found or is missing handle/DID.", { scoreId: args.scoreId });
      return { success: false, error: "Score not found or is missing handle/DID." };
    }

    const leaderboardPage = await ctx.runQuery(api.index.getHighScores, { paginationOpts: { numItems: 4 } });
    const leaderboard = leaderboardPage.page;

    // 2. Find the rank of the new score
    const rank = leaderboard.findIndex(s => s._id === args.scoreId) + 1;

    if (rank === 0 || rank > 3) {
      console.log(`Score ${args.scoreId} is not in the top 3 (rank ${rank}). No announcement will be made.`);
      return { success: true, message: "Not in top 3, no announcement needed." };
    }

    // 3. Determine the displaced players and format the message
    let postText = "";
    const mentions = [{ handle: newScore.blueskyHandle, did: newScore.blueskyDid }];
    const achiever = `@${newScore.blueskyHandle}`;

    if (rank === 1) {
      const previousRank1 = leaderboard[1];
      const pushedDownHandles = [leaderboard[2], leaderboard[3]]
        .filter(p => p && p.blueskyHandle)
        .map(p => `@${p.blueskyHandle}`);
      
      if (previousRank1 && previousRank1.blueskyHandle && previousRank1.blueskyDid) {
        mentions.push({ handle: previousRank1.blueskyHandle, did: previousRank1.blueskyDid });
      }
      [leaderboard[2], leaderboard[3]].forEach(p => {
        if (p && p.blueskyHandle && p.blueskyDid) {
          mentions.push({ handle: p.blueskyHandle, did: p.blueskyDid });
        }
      });

      const overthroned = previousRank1 ? `@${previousRank1.blueskyHandle}` : "the void";
      const pushedText = pushedDownHandles.length > 0 ? ` and pushed down the ladder ${pushedDownHandles.join(" and ")}` : "";
      postText = `Hail to the new king! ${achiever} 👑 topped the leaderboards, overthrowing ${overthroned}${pushedText}. May their legacy be remembered!`;
    } else if (rank === 2) {
      const pushedDownHandles = [leaderboard[2], leaderboard[3]]
        .filter(p => p && p.blueskyHandle)
        .map(p => `@${p.blueskyHandle}`);

      [leaderboard[2], leaderboard[3]].forEach(p => {
        if (p && p.blueskyHandle && p.blueskyDid) {
          mentions.push({ handle: p.blueskyHandle, did: p.blueskyDid });
        }
      });
      
      const pushedText = pushedDownHandles.length > 0 ? ` pushing down the ladder ${pushedDownHandles.join(" and ")}` : "";
      postText = `A marvelous achievement by ${achiever}, who made a move to the 2nd spot${pushedText}! 🥈 #RetroGaming #ArcadeInvaders`;
    } else if (rank === 3) {
      const previousRank3 = leaderboard[3];
      const pushedUser = previousRank3 ? `@${previousRank3.blueskyHandle}` : "a ghost";
      if (previousRank3 && previousRank3.blueskyHandle && previousRank3.blueskyDid) {
        mentions.push({ handle: previousRank3.blueskyHandle, did: previousRank3.blueskyDid });
      }
      postText = `An astonishing move by ${achiever}, who claimed rank 🥉, pushing ${pushedUser} out of the master's area!`;
    }

    // 4. Post to Bluesky
    if (postText) {
      try {
        const session = await getBlueskySession();
        const uniqueMentions = [...new Map(mentions.map(item => [item.handle, item])).values()];
        await postToBluesky(session.accessJwt, postText, uniqueMentions);
        return { success: true };
      } catch (error) {
        console.error("Failed to post top rank announcement:", error);
        return { success: false, error: "Failed to post to Bluesky." };
      }
    }
    return { success: true, message: "No post text generated." };
  },
});
