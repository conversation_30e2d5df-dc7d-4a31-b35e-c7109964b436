import React, { useState, useEffect } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Doc } from '../../../convex/_generated/dataModel';

const PAGE_SIZE = 100; // Number of scores per page
const MAX_RESULTS = 1000; // Maximum number of results to paginate through

interface LeaderboardScreenProps {
  setGameState: (state: 'menu' | 'playing' | 'gameOver' | 'options' | 'submitScore' | 'leaderboard') => void;
}

export const LeaderboardScreen: React.FC<LeaderboardScreenProps> = ({ setGameState }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [cursors, setCursors] = useState<(string | null)[]>([null]); // Store cursor for each page start
  const [displayedScores, setDisplayedScores] = useState<Doc<"scores">[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLastPageOverall, setIsLastPageOverall] = useState(false);

  // Search state
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const currentCursor = cursors[currentPage -1];

  // Debounce search term
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms debounce

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm]);

const paginatedHighScores = useQuery(
  api.index.getHighScores,
  (isSearching || debouncedSearchTerm.trim() !== '') // Condition to skip
    ? "skip" // Pass "skip" to disable
    : { paginationOpts: { cursor: currentCursor === null ? undefined : currentCursor, numItems: PAGE_SIZE } } // Args when not skipping
);



const searchResults = useQuery(
  api.index.getScoresByBlueskyHandle,
  (!isSearching || debouncedSearchTerm.trim() === '') // Condition to skip
    ? "skip" // Pass "skip" to disable
    : { blueskyHandle: debouncedSearchTerm.trim() } // Args when not skipping
);



  useEffect(() => {
    if (debouncedSearchTerm.trim() !== '') {
      setIsSearching(true);
      setIsLoading(true);
    } else {
      setIsSearching(false);
      // Potentially reset to page 1 of general scores or retain current page
      // For now, let's just ensure loading state is managed for general scores
      if (!paginatedHighScores) setIsLoading(true);
    }
  }, [debouncedSearchTerm, paginatedHighScores]);


  useEffect(() => {
    if (isSearching) {
      if (searchResults) {
        setDisplayedScores(searchResults);
        setIsLoading(false);
      } else if (debouncedSearchTerm.trim() !== '') {
        // Still waiting for search results
        setIsLoading(true);
      }
    } else {
      // Handling general paginated scores
      if (paginatedHighScores) {
        setDisplayedScores(paginatedHighScores.page);
        if (paginatedHighScores.isDone && cursors.length === currentPage) {
          setIsLastPageOverall(true);
        } else if (!paginatedHighScores.isDone && cursors.length === currentPage) {
          setCursors(prev => [...prev, paginatedHighScores.continueCursor]);
          setIsLastPageOverall(false);
        }
        setIsLoading(false);
      } else {
        setIsLoading(true);
      }
    }
  }, [paginatedHighScores, searchResults, isSearching, debouncedSearchTerm, currentPage, cursors]);

  const handleNextPage = () => {
    if (!isSearching && !isLastPageOverall && (currentPage * PAGE_SIZE < MAX_RESULTS)) {
      setIsLoading(true);
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (!isSearching && currentPage > 1) {
      setIsLoading(true);
      setCurrentPage(prev => prev - 1);
      setIsLastPageOverall(false); 
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };
  
  const scoresToShow = displayedScores;
  // const totalFetchedPages = cursors.length -1; // -1 because the first cursor is null for the first page

  return (
    <div className="flex flex-col items-center justify-center min-w-full  min-h-screen text-red-500 px-4 py-8">
      <div className='flex w-full items-start'>
      <button
        className="auth-button text-lg mb-6 !py-0 !w-[30%]"
        onClick={() => setGameState('menu')}
      >
        BACK TO MENU
      </button>
      </div>
      <h1 className="text-8xl mb-4 text-center animate-pulse">Leaderboards</h1>
      
      <div className="mb-6 w-full max-w-md">
        <input 
          type="text" 
          placeholder="Search by Bluesky Handle (e.g. @user.bsky.social)" 
          className="auth-input-field lowercase text-center"
          value={searchTerm}
          onChange={handleSearchChange}
        />
      </div>

      {isLoading && <p className="text-xl my-4">Loading scores...</p>}
      
      {!isLoading && scoresToShow?.length > 0 ? (
        <div className="w-full max-w-4xl">
          <div className="border-2 border-red-500 overflow-hidden shadow-lg shadow-red-500/30">
            <table className="w-full text-left">
              <thead className="bg-red-900">
                <tr>
                  <th className="p-3 text-sm sm:text-base">Rank</th>
                  <th className="p-3 text-sm sm:text-base">Player</th>
                  <th className="p-3 text-sm sm:text-base">Score</th>
                  <th className="p-3 text-sm sm:text-base hidden md:table-cell">Level</th>
                  <th className="p-3 text-sm sm:text-base hidden md:table-cell">Enemies Killed</th>
                </tr>
              </thead>
              <tbody>
                {scoresToShow.map((score, i) => (
                  <tr key={score._id} className="border-t border-red-800 hover:bg-red-900/30 transition-colors">
                    {/* Rank calculation needs to consider if it's search result or paginated */}
                    <td className="p-3 text-sm sm:text-base">{isSearching ? i + 1 : (currentPage - 1) * PAGE_SIZE + i + 1}</td>
                    <td className="p-3 text-sm sm:text-base truncate max-w-[100px] sm:max-w-xs">{score.playerName}</td>
                    <td className="p-3 text-sm sm:text-base">{score.score}</td>
                    <td className="p-3 text-sm sm:text-base hidden md:table-cell">{score.level}</td>
                    <td className="p-3 text-sm sm:text-base hidden md:table-cell">{score.enemiesKilled}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        !isLoading && (isSearching && debouncedSearchTerm.trim() !== '' ? 
          <p className="text-xl my-4">No scores found for "{debouncedSearchTerm}".</p> : 
          <p className="text-xl my-4">No high scores yet. Be the first!</p>)
      )}

      <div className="mt-6 flex items-center justify-center gap-4 w-full max-w-xs">
        <button
          onClick={handlePrevPage}
          disabled={isSearching || currentPage === 1 || isLoading}
          className="auth-button px-6 py-2 text-lg disabled:opacity-50 disabled:cursor-not-allowed"
        >
          PREVIOUS
        </button>
        {!isSearching && <span className="text-lg">Page {currentPage}</span>}
        {isSearching && <span className="text-lg mx-4">-</span>} 
        <button
          onClick={handleNextPage}
          disabled={isSearching || isLastPageOverall || isLoading || (currentPage * PAGE_SIZE >= MAX_RESULTS)}
          className="auth-button px-6 py-2 text-lg disabled:opacity-50 disabled:cursor-not-allowed"
        >
          NEXT
        </button>
      </div>
      
      <button
        className="auth-button text-xl mt-8 w-full max-w-xs"
        onClick={() => setGameState('menu')}
      >
        BACK TO MENU
      </button>
    </div>
  );
};
