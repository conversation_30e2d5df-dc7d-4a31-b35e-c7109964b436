import React, { useEffect, useCallback } from 'react';

type GameState = 'menu' | 'playing' | 'gameOver' | 'options' | 'submitScore' | 'leaderboard';

interface DisplayModeManagerProps {
  gameWrapperRef: React.RefObject<HTMLDivElement | null>; // May not be needed anymore for maximization logic here
  isMaximized: boolean; // Renamed
  onToggleMaximized: () => void; // Renamed
  orientation: 'horizontal' | 'vertical'; // Will be used in Phase 2
  setOrientation: (orientation: 'horizontal' | 'vertical') => void; // Will be used in Phase 2
  currentGameState: GameState;
}

const DisplayModeManager: React.FC<DisplayModeManagerProps> = ({
  // gameWrapperRef, // No longer directly used by DisplayModeManager for fullscreen/maximization logic
  isMaximized, // Renamed
  onToggleMaximized, // Renamed
  orientation,
  setOrientation,
  currentGameState,
}) => {
  // Fullscreen API related listeners are removed as we are doing viewport maximization via CSS in App.tsx

  const showOverlayButtons = currentGameState === 'menu' || currentGameState === 'playing';

  if (!showOverlayButtons) {
    return null;
  }

  return (
    <div className="absolute top-2 right-2 z-50 flex flex-col gap-2">
      <button
        onClick={onToggleMaximized} // Renamed
        className="hidden sm:block absolute top-2 right-2 text-red-500 border-2 border-red-500 px-8 py-1 hover:bg-red-500 hover:text-black transition-colors"
        title={isMaximized ? 'Minimize View' : 'Maximize View'} // Updated title
      >
        {isMaximized ? 'Min [-]' : 'Max [+]'} {/* Updated text */}
      </button>
      {/* Vertical mode button will be added here in Phase 2 */}
      {/* 
      <button
        onClick={() => setOrientation(orientation === 'horizontal' ? 'vertical' : 'horizontal')}
        className="p-2 bg-gray-700 text-white rounded hover:bg-gray-600 text-xs"
        title="Toggle Orientation"
      >
        {orientation === 'horizontal' ? 'Vert' : 'Horiz'}
      </button>
      */}
    </div>
  );
};

export default DisplayModeManager;
